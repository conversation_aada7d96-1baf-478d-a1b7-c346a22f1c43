---
resources:
  repositories:
    - repository: templates
      type: git
      name: pipeline-transversal
      ref: refs/heads/main

trigger:
  branches:
    include:
      - '*'
    exclude:
      - main

pr:
  branches:
    include:
      - '*'

variables:
  - template: variables-main.yml
  - group: cmdb-sura-variables-group-lab
  - group: sonarqube-variables-group

extends:
  template: pipeline-template.yml@templates
  parameters:
    imageRepository: ${{ variables.imageRepository }}
    dockerfilePath: ${{ variables.dockerfilePath }}
    tag: ${{ variables.tag }}
    technology: ${{ variables.technology }}
    nodeVersion: ${{ variables.nodeVersion }}
    projectKey: ${{ variables.projectKey }}
