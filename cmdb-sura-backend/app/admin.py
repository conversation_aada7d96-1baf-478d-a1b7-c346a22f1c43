"""
Configuración del sitio de administración Django para el sistema CMDB.

Este módulo registra todos los modelos del sistema de gestión de inventario
en la interfaz de administración de Django, proporcionando una interfaz
web intuitiva para la gestión de datos maestros y registros de inventario.

Dependencies:
    - django.contrib.admin: Framework de administración de Django
    - .models: Modelos de datos del sistema CMDB
"""

from django.contrib import admin
from .models import Inventory, TypeDevice, BaseCity, BaseHeadquarters
from .models import Company, Area, ModalityTag, Manufacturer, StatusDevice

admin.site.register(Inventory)
admin.site.register(TypeDevice)
admin.site.register(BaseCity)
admin.site.register(BaseHeadquarters)
admin.site.register(Company)
admin.site.register(Area)
admin.site.register(ModalityTag)
admin.site.register(Manufacturer)
admin.site.register(StatusDevice)
