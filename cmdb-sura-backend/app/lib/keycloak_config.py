"""
Cliente de autenticación Keycloak para integración OAuth2/OpenID Connect.

Este módulo proporciona una interfaz completa para interactuar con un servidor
Keycloak como proveedor de identidad, implementando flujos OAuth2 estándar
para autenticación, autorización y gestión de tokens en el sistema CMDB.

Components:
    - KeycloakAuth: Cliente principal para operaciones OAuth2/OIDC

Configuration Requirements:
    Requiere configuración en settings.py:
    - BASE_URL: Endpoint del realm de Keycloak
    - CLIENT_ID: Identificador del cliente registrado
    - CLIENT_SECRET: Secreto del cliente para autenticación

Dependencies:
    - requests: Cliente HTTP para comunicación con Keycloak
    - base64: Codificación para autenticación Basic
    - django.conf.settings: Configuración de conexión
    - logging: Auditoría y debugging de operaciones
"""

import base64
import logging
import requests
from django.conf import settings
from rest_framework import exceptions

logging.basicConfig(format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("keycloakAuth")


class KeycloakAuthenticationFailed(exceptions.AuthenticationFailed):
    """
    Excepción personalizada para errores de autenticación de keycloak.

    Extiende la excepción base AuthenticationFailed de DRF para proporcionar
    mensajes de error personalizados en español y códigos de estado HTTP
    específicos para diferentes escenarios de fallo de autenticación.

    Attributes:
        status_code: Código HTTP 400 (Bad Request) para errores de autenticación
        default_detail: Mensaje genérico para credenciales inválidas
        default_code: Código interno para clasificación de errores
    """

    status_code = 400
    default_detail = "Error de autenticación con keycloak."
    default_code = "bad_request"


class KeycloakAuth:
    """
    Cliente OAuth2/OpenID Connect para autenticación con servidor Keycloak.

    Implementa los flujos OAuth2 estándar para autenticación de usuarios,
    gestión de tokens y validación de sesiones, proporcionando integración
    completa con Keycloak como proveedor de identidad centralizado.

    Token Types:
        - Access Token: JWT con claims de usuario y permisos
        - Refresh Token: Token opaco para renovación de sesión
        - ID Token: JWT con información de identidad (OIDC)
    """

    def __init__(self) -> None:
        self.base_url = settings.KEYCLOAK["BASE_URL"]
        self.client_id = settings.KEYCLOAK["CLIENT_ID"]
        self.client_secret = settings.KEYCLOAK["CLIENT_SECRET"]

    def get_user_token(self, login_dto: dict[str, str]) -> dict[str, str]:
        """
        Obtiene tokens de acceso usando credenciales de usuario (Resource Owner Password Grant).

        Implementa el flujo OAuth2 Resource Owner Password Credentials Grant
        para autenticación directa con username y password, intercambiando
        credenciales por tokens de acceso y refresh.

        Args:
            login_dto: Diccionario con credenciales del usuario
                    {"username": "usuario", "password": "contraseña"}

        Returns:
            dict: Respuesta con tokens o mensaje de error
                  Success: {"token": "access_token", "refreshToken": "refresh_token"}
        """

        data = {
            "grant_type": "password",
            "client_id": self.client_id,
            "username": login_dto["username"],
            "password": login_dto["password"],
            "client_secret": self.client_secret,
        }
        try:
            response = requests.post(self.base_url, data=data, timeout=settings.KEYCLOAK["TIMEOUT"])
            response.raise_for_status()
            response_data = response.json()
            return {
                "token": response_data["access_token"],
                "refreshToken": response_data["refresh_token"],
            }
        except requests.HTTPError as e:
            if response.status_code in [401, 403]:
                raise KeycloakAuthenticationFailed(
                    "Usuario o contraseña incorrectos."
                ) from e
            logger.error("Error al validar las credenciales: %s", str(e))
            raise KeycloakAuthenticationFailed(
                "Error al validar las credenciales. Contacte con el administrador."
            ) from e

    def get_user_token_with_code(
        self, code: str, redirect_url: str
    ) -> dict[str] | dict[str, str]:
        """
        Intercambia código de autorización por tokens (Authorization Code Grant).

        Implementa el flujo OAuth2 Authorization Code Grant para obtener tokens
        después de que el usuario se ha autenticado en Keycloak y ha sido
        redirigido con un código de autorización temporal.

        Args:
            code: Código de autorización temporal de Keycloak
            redirect_url: URL de redirección utilizada en el flujo OAuth2

        Returns:
            dict: Respuesta con tokens completos o mensaje de error
                Success: {"token": "access_token", "refreshToken": "refresh_token", "id_token": "id_token"}
        """

        data = {
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "redirect_uri": redirect_url,
        }
        try:
            response = requests.post(self.base_url, data=data, timeout=settings.KEYCLOAK["TIMEOUT"])
            response.raise_for_status()
            response_data = response.json()
            return {
                "token": response_data["access_token"],
                "refreshToken": response_data["refresh_token"],
                "id_token": response_data["id_token"],
            }
        except requests.HTTPError as e:
            if response.status_code in [401, 403]:
                logging.warning("Código de autorización inválido o expirado: %s", str(e))
                raise KeycloakAuthenticationFailed(
                    "Código de autorización inválido o expirado."
                ) from e
            logger.error("Error de autenticación: %s", str(e))
            raise KeycloakAuthenticationFailed(
                "Error de autenticación, Contacte con el administrador."
            ) from e

    def inspect_token(self, token: str) -> dict[str, str]:
        """
        Valida e inspecciona tokens de acceso con el servidor Keycloak.

        Implementa OAuth2 Token Introspection para validar tokens de acceso
        y obtener información sobre su estado, expiración y claims contenidos,
        utilizando el endpoint de introspección de Keycloak.

        Args:
            token: Token de acceso a validar e inspeccionar

        Returns:
            dict: Información del token o mensaje de error
                Success: Claims del token (active, exp, username, etc.)
        """
        url = f"{self.base_url}/introspect"
        auth_header = (
            "Basic "
            + base64.b64encode(
                f"{self.client_id}:{self.client_secret}".encode()
            ).decode()
        )
        headers = {"Authorization": auth_header}
        data = {"token": token}
        try:
            response = requests.post(url, data=data, headers=headers, timeout=settings.KEYCLOAK["TIMEOUT"])
            response.raise_for_status()
            return response.json()
        except requests.HTTPError as e:
            logger.error("Error en la validación del token: %s", str(e))
            raise KeycloakAuthenticationFailed(
                "El token enviado no es válido."
            ) from e

    def refresh_token(self, refresh_token: str) -> dict[str, str]:
        """
        Renueva tokens de acceso usando refresh token válido.

        Implementa OAuth2 Refresh Token Grant para obtener nuevos tokens de acceso
        sin requerir re-autenticación del usuario, utilizando un refresh token
        válido previamente obtenido durante el proceso de autenticación inicial.

        Args:
            refresh_token: Token de refresh válido para renovación

        Returns:
            dict: Nuevos tokens o mensaje de error
                Success: {"token": "new_access_token", "refreshToken": "new_refresh_token"}
        """

        data = {
            "grant_type": "refresh_token",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token,
        }
        try:
            response = requests.post(self.base_url, data=data, timeout=settings.KEYCLOAK["TIMEOUT"])
            response.raise_for_status()
            response_data = response.json()
            return {
                "token": response_data["access_token"],
                "refreshToken": response_data["refresh_token"],
            }
        except requests.HTTPError as e:
            logger.error("Error al actualizar el token: %s", str(e))
            raise KeycloakAuthenticationFailed(
                "No es posible actualizar el estado de su sesión actualmente."
            ) from e
