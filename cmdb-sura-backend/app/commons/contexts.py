"""
Clases de contexto y configuración para procesamiento de datos de inventario.

Este módulo define dataclasses especializadas que encapsulan contextos de datos
y configuraciones utilizadas durante el procesamiento de archivos Excel,
validación de campos y generación de plantillas del sistema de inventario CMDB.

Components:
   Context Data Classes:
       - CommonContextData: Contexto base para procesamiento de filas Excel
       - FKContextData: Contexto especializado para campos ForeignKey
       - ValidationContextData: Contexto para validaciones de campos
       - FieldContextData: Contexto para campos con lógica condicional

   Configuration Classes:
       - HeaderStyle: Configuración de estilos para encabezados Excel
       - CommonMeta: Configuración reutilizable para serializers

Dependencies:
   - dataclasses: Decoradores para clases de datos Python
   - pandas: DataFrames para procesamiento de datos
   - openpyxl.styles: Estilos para documentos Excel
   - django.db.models: Integración con modelos Django
"""

from dataclasses import dataclass
import pandas as pd
from openpyxl.styles import Font, PatternFill, Alignment, Border
from django.db import models


@dataclass
class CommonContextData:
    """
    Contexto común para procesar campos de inventario.

    Attributes:
        row: Diccionario con los datos de una fila del Excel.
        inventory_df: DataFrame de pandas con el inventario completo.1
        row_number: Número de fila actual en procesamiento.
    """

    row: dict[str:str]
    inventory_df: pd.DataFrame
    row_number: int


@dataclass
class FKContextData(CommonContextData):
    """
    Contexto para procesar campos de tipo ForeignKey.

    Extends:
        CommonContextData

    Attributes:
        excel_field: Nombre del campo en el archivo Excel.
        model_field: Nombre del campo en el modelo de Django.
        model_class: Clase del modelo de Django relacionado.
        required: Indica si el campo es obligatorio.
        inventory_data: Diccionario con los datos del inventario procesados.
    """

    excel_field: str
    model_field: str
    model_class: models.Model
    required: bool
    inventory_data: dict[str, str]


@dataclass
class ValidationContextData(CommonContextData):
    """
    Contexto para procesar validación de campos obligatorios.

    Extends:
        CommonContextData

    Attributes:
        excel_field: Nombre del campo en el archivo Excel.
        model_field: Nombre del campo en el modelo de Django.
    """

    excel_field: str
    model_field: str
    to_update: bool | None = None
    action: str | None = None


@dataclass
class FieldContextData(CommonContextData):
    """
    Contexto para procesar campos de inventario con lógica condicional.

    Extends:
        CommonContextData

    Attributes:
        common_flag: Bandera que indica si aplica lógica común para el campo.
        inventory_data: Diccionario con los datos del inventario procesados.
    """

    common_flag: bool
    inventory_data: dict[str, str]


@dataclass
class HeaderStyle:
    """
    Estilo de encabezado para las hojas de Excel.

    Attributes:
        font: Fuente del encabezado.
        fill: Relleno del encabezado.
        alignment: Alineación del texto en el encabezado.
        border: Bordes del encabezado.
    """

    font: Font
    fill: PatternFill
    alignment: Alignment
    border: Border


@dataclass
class CommonMeta:
    """
    Clase para reutilizar configuración de campos completos en serializers.

    Proporciona configuración estándar para serializers que requieren
    todos los campos del modelo, evitando repetición de código.

    Attributes:
        fields: Campos del modelo a utilizar, por defecto incliye todos los campos.
    """

    fields: list[str] | str = "__all__"
