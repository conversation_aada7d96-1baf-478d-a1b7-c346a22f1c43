"""
Constantes y configuraciones del sistema de gestión de inventario CMDB.

Este módulo centraliza todas las constantes utilizadas en el sistema para
la gestión de inventario, incluyendo definiciones de campos, opciones de
configuración y rangos de validación para operaciones de importación/exportación.

Components:
    Field Definitions:
        - HEADERS: Lista ordenada de campos para exportación/importación Excel

    Configuration Options:
        - DEVICE_CLASS_CHOISES: Opciones de clasificación de dispositivos

    Validation Ranges:
        - VALIDATION_RANGES: Rangos de celdas para validaciones Excel

Dependencies:
    - app.commons.enums: Enumeraciones de campos del sistema
"""

from app.commons.enums import InventoryFields

HEADERS = [
    InventoryFields.BASE_CITY.value,
    InventoryFields.BASE_HEADQUARTERS.value,
    InventoryFields.LOCATION.value,
    InventoryFields.DOCUMENT.value,
    InventoryFields.COMPANY.value,
    InventoryFields.USERNAME.value,
    InventoryFields.TYPE_DEVICE.value,
    InventoryFields.AREA.value,
    InventoryFields.FLOOR.value,
    InventoryFields.MANUFACTURER.value,
    InventoryFields.MODEL.value,
    InventoryFields.SERIAL.value,
    InventoryFields.LICENSE_PLATE.value,
    InventoryFields.OWNERSHIP.value,
    InventoryFields.WAREHOUSE_ENTRY_DATE.value,
    InventoryFields.NETWORK_USER.value,
    InventoryFields.MODALITY.value,
    InventoryFields.MODALITY_TAG.value,
    InventoryFields.COMMENTS.value,
    InventoryFields.STATUS.value,
    InventoryFields.MACHINE_NAME.value,
    InventoryFields.WARRANTY_DATE.value,
    InventoryFields.OS.value,
    InventoryFields.PROCESSOR_AND_SPEED.value,
    InventoryFields.HARD_DRIVE.value,
    InventoryFields.MEMORY.value,
    InventoryFields.TOTAL_MEMORY_BANKS.value,
    InventoryFields.USED_BANKS.value,
    InventoryFields.FREE_BANKS.value,
    InventoryFields.TECHNOLOGY_RENEWAL_DATE.value,
]

HEADERS_DELETE = [InventoryFields.SERIAL.value, InventoryFields.DELETE_CAUSE.value]

DEVICE_CLASS_CHOISES = [
    ("Movil", "Movil"),
    ("Fijo", "Fijo"),
]

VALIDATION_RANGES = {
    "A": "A2:A1000",
    "B": "B2:B1000",
    "C": "E2:E1000",
    "D": "G2:G1000",
    "E": "H2:H1000",
    "F": "J2:J1000",
    "G": "T2:T1000",
    "H": "R2:R1000",
}
