"""
Enumeraciones para campos y etiquetas del sistema de inventario CMDB.

Este módulo centraliza todas las enumeraciones utilizadas en el sistema para
mantener consistencia en la nomenclatura de campos, etiquetas de interfaz
y validaciones, proporcionando un punto único de configuración para textos
y referencias de campos en todo el sistema.

Components:
    Field Enumerations:
        - InventoryFields: Campos específicos del modelo de inventario
        - ModelFields: Campos comunes utilizados en múltiples modelos

Dependencies:
    - enum.Enum: Enumeraciones estándar de Python para type safety
"""

from enum import Enum


class InventoryFields(Enum):
    """
    Enumeración de campos específicos del modelo de inventario de dispositivos.

    Define todos los campos utilizados en el modelo principal Inventory con sus
    etiquetas en español para uso consistente en interfaces de usuario, formularios,
    documentos Excel y documentación de APIs del sistema.
    """

    BASE_CITY = "Ciudad base"
    BASE_HEADQUARTERS = "Sede base"
    LOCATION = "Ubicación"
    DOCUMENT = "Cédula"
    COMPANY = "Compañía"
    USERNAME = "Nombre de usuario"
    TYPE_DEVICE = "Tipo de dispositivo"
    AREA = "Área"
    FLOOR = "Piso"
    MANUFACTURER = "Fabricante"
    MODEL = "Modelo"
    SERIAL = "Serial"
    LICENSE_PLATE = "Placa"
    OWNERSHIP = "Propiedad"
    WAREHOUSE_ENTRY_DATE = "Fecha de ingreso a bodega"
    NETWORK_USER = "Usuario de red"
    MODALITY = "Modalidad"
    MODALITY_TAG = "Etiqueta de modalidad"
    COMMENTS = "Observaciones"
    STATUS = "Estado de dispositivo"
    MACHINE_NAME = "Nombre de máquina"
    WARRANTY_DATE = "Fecha de garantía"
    OS = "SO"
    PROCESSOR_AND_SPEED = "Procesador y velocidad"
    HARD_DRIVE = "Disco duro"
    MEMORY = "Memoria"
    TOTAL_MEMORY_BANKS = "Total bancos de memoria"
    USED_BANKS = "Bancos usados"
    FREE_BANKS = "Bancos libres"
    TECHNOLOGY_RENEWAL_DATE = "Fecha de renovación tecnológica"
    DELETE_CAUSE = "Causal de baja"


class ModelFields(Enum):
    """
    Enumeración de campos comunes utilizados en campos maestros.

    Define campos estándar que aparecen consistentemente en varios modelos
    del sistema, especialmente en entidades maestras que heredan de CommonModel,
    proporcionando nomenclatura uniforme para funcionalidades transversales.
    """

    NAME = "Nombre"
    STATE = "Estado"
    IS_DELETED = "Está eliminado"
