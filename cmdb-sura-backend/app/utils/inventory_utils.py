"""
Utilidades de procesamiento de datos para importación de inventario desde Excel.

Este módulo contiene funciones especializadas para el procesamiento, validación
y transformación de datos de inventario importados desde archivos Excel,
implementando lógica de negocio específica y validaciones de integridad.

Components:
    Excel Processing:
        - fill_inventory_data: Escritura de datos de inventario a Excel
        - get_header_style: Configuración de estilos para encabezados

    Row Processing:
        - process_single_row: Orquestador principal de validación por fila
        - process_foreign_keys: Validación de relaciones con entidades maestras
        - process_fields: Validación de campos requeridos, opcionales y únicos
        - process_dates: Validación y conversión de campos de fecha

    Field Validation:
        - process_area_field: Validación condicional de área por tipo dispositivo
        - process_floor_field: Validación condicional de piso por tipo dispositivo
        - process_modality_tag: Validación condicional de etiqueta de modalidad
        - validate_required_field: Validación de campos obligatorios
        - validate_duplicate_field: Validación de unicidad en base de datos

    Specialized Processing:
        - process_single_fk: Validación individual de campos ForeignKey
        - process_device_type_logic: Lógica condicional según tipo de dispositivo
        - process_single_date: Conversión y validación de fechas individuales
"""

from django.db import DatabaseError
from django.utils import timezone
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import pandas as pd
from app.commons.contexts import (
    FKContextData,
    FieldContextData,
    HeaderStyle,
    ValidationContextData,
)
from app.commons.enums import InventoryFields
from app.models import (
    Area,
    BaseCity,
    BaseHeadquarters,
    Company,
    Inventory,
    Manufacturer,
    ModalityTag,
    StatusDevice,
    TypeDevice,
)


def fill_inventory_data(work_sheet: Worksheet, data: Inventory) -> None:
    """
    Llena la hoja de trabajo Excel con datos de inventario.

    Args:
    work_sheet (Worksheet): La hoja de trabajo a llenar.
    data (Inventory): Los datos de inventario a escribir.
    """
    work_sheet.append(
        [
            data.base_city.name,
            data.base_headquarters.name,
            data.location,
            data.document,
            data.company.name,
            data.username,
            data.type_device.name,
            data.area.name if data.area is not None else "",
            data.floor,
            data.manufacturer.name,
            data.model,
            data.serial,
            data.license_plate,
            data.ownership,
            data.warehouse_entry_date.strftime("%Y-%m-%d %H:%M:%S"),
            data.network_user,
            data.modality,
            data.modality_tag.name if data.modality_tag is not None else "",
            data.comments,
            data.status.name,
            data.machine_name,
            data.warranty_date.strftime("%Y-%m-%d %H:%M:%S"),
            data.os,
            data.processor_and_speed,
            data.hard_drive,
            data.memory,
            data.total_memory_banks,
            data.used_banks,
            data.free_banks,
            data.technology_renewal_date.strftime("%Y-%m-%d %H:%M:%S"),
        ]
    )


def get_header_style() -> HeaderStyle:
    """
    Define el estilo visual para los encabezados de la plantilla.

    Returns:
        HeaderStyle: Objeto con configuración de fuente, relleno,
                alineación y bordes para encabezados.
    """
    return HeaderStyle(
        font=Font(bold=True, color="FFFFFF"),
        fill=PatternFill(start_color="366092", end_color="366092", fill_type="solid"),
        alignment=Alignment(horizontal="center", vertical="center", wrap_text=True),
        border=Border(
            left=Side(border_style="thin", color="000000"),
            right=Side(border_style="thin", color="000000"),
            top=Side(border_style="thin", color="000000"),
            bottom=Side(border_style="thin", color="000000"),
        ),
    )


def process_area_field(args: FieldContextData) -> dict[str, str] | None:
    """
    Procesa campo Área con validación condicional por tipo de dispositivo.

    Para dispositivos fijos: campo requerido con validación de existencia.
    Para dispositivos móviles: campo debe estar vacío.

    Args:
        args: Contexto con datos del campo y flags condicionales.

    Returns:
        dict | None: Error si validación falla, None si es válido.
    """
    column_index = args.inventory_df.columns.get_loc(InventoryFields.AREA.value)
    column_letter = get_column_letter(column_index + 1)
    return_message = None
    if args.common_flag:
        if args.row.get(InventoryFields.AREA.value):
            try:
                area = Area.objects.filter(
                    name=args.row[InventoryFields.AREA.value]
                ).first()
                if area:
                    args.inventory_data["area"] = area
                else:
                    return_message = {
                        "cell": f"{column_letter}{args.row_number}",
                        "error": f"No existe el área: {args.row[InventoryFields.AREA.value]}",
                    }
            except DatabaseError as e:
                return_message = {
                    "cell": f"{column_letter}{args.row_number}",
                    "error": f"Error consultar el campo Área: {str(e)}",
                }
        else:
            return_message = {
                "cell": f"{column_letter}{args.row_number}",
                "error": "Área es requerida para dispositivos fijos",
            }
    else:
        if args.row.get("Área"):
            return_message = {
                "cell": f"{column_letter}{args.row_number}",
                "error": "Área sólo aplica a dispositivos fijos",
            }
        args.inventory_data["area"] = None
    return return_message


def process_floor_field(args: FieldContextData) -> dict[str, str] | None:
    """
    Procesa campo Piso con validación condicional por tipo de dispositivo.

    Para dispositivos fijos: campo requerido.
    Para dispositivos móviles: campo debe estar vacío.

    Args:
        args: Contexto con datos del campo y flags condicionales.

    Returns:
        dict | None: Error si validación falla, None si es válido.
    """
    column_index = args.inventory_df.columns.get_loc(InventoryFields.FLOOR.value)
    column_letter = get_column_letter(column_index + 1)
    if args.common_flag:
        if args.row.get(InventoryFields.FLOOR.value):
            args.inventory_data["floor"] = args.row.get(InventoryFields.FLOOR.value)
        else:
            return {
                "cell": f"{column_letter}{args.row_number}",
                "error": "Piso es requerido para dispositivos fijos",
            }
    else:
        if args.row.get(InventoryFields.FLOOR.value):
            return {
                "cell": f"{column_letter}{args.row_number}",
                "error": "Piso sólo aplica a dispositivos fijos",
            }
        args.inventory_data["floor"] = ""
    return None


def process_modality_tag(args: FieldContextData) -> dict[str, str] | None:
    """
    Procesa campo Etiqueta de Modalidad con validación condicional.

    Para dispositivos móviles: campo opcional con validación de existencia.
    Para dispositivos fijos: campo debe estar vacío.

    Args:
        args: Contexto con datos del campo y flags condicionales.

    Returns:
        dict | None: Error si validación falla, None si es válido.
    """
    column_index = args.inventory_df.columns.get_loc(InventoryFields.MODALITY_TAG.value)
    column_letter = get_column_letter(column_index + 1)
    return_message = None
    if args.common_flag:
        if args.row.get(InventoryFields.MODALITY_TAG.value):
            try:
                modality_tag = ModalityTag.objects.filter(
                    name=args.row[InventoryFields.MODALITY_TAG.value]
                ).first()
                if modality_tag:
                    args.inventory_data["modality_tag"] = modality_tag
                else:
                    return_message = {
                        "cell": f"{column_letter}{args.row_number}",
                        "error": f"""No existe la Etiqueta de Modalidad:
                            {args.row[InventoryFields.MODALITY_TAG.value]}""",
                    }
            except DatabaseError as e:
                return_message = {
                    "cell": f"{column_letter}{args.row_number}",
                    "error": f"Error consultar el campo Etiqueta de Modalidad: {str(e)}",
                }
        else:
            args.inventory_data["modality_tag"] = None
    else:
        if args.row.get(InventoryFields.MODALITY_TAG.value):
            return_message = {
                "cell": f"{column_letter}{args.row_number}",
                "error": "Etiqueta de Modalidad sólo aplica a dispositivos móviles",
            }
        args.inventory_data["modality_tag"] = None
    return return_message


def process_single_row(
    row: dict[str:str], inventory_df: pd.DataFrame, row_number: int, action: str
) -> tuple[dict[str, str], dict[str, str], list[dict[str, str]]]:
    """
    Procesa una fila individual del Excel con validaciones completas.

    Ejecuta todas las validaciones necesarias: campos ForeignKey,
    campos requeridos/opcionales y fechas, acumulando errores.

    Args:
        row: Diccionario con datos de la fila actual.
        inventory_df: DataFrame completo para contexto.
        row_number: Número de fila para reporte de errores.

    Returns:
        tuple: Diccionario con datos válidos del inventario y
                lista de errores encontrados en la fila.
    """
    inventory_data = {}
    row_errors = []
    update_data, field_errors = process_fields(
        row, inventory_df, row_number, inventory_data, action
    )
    row_errors.extend(field_errors)
    if action == "create":
        fk_errors = process_foreign_keys(row, inventory_df, row_number, inventory_data)
        row_errors.extend(fk_errors)
        date_errors = process_dates(row, inventory_df, row_number, inventory_data)
        row_errors.extend(date_errors)
    return update_data, inventory_data, row_errors


def process_foreign_keys(
    row: dict[str:str],
    inventory_df: pd.DataFrame,
    row_number: int,
    inventory_data: dict[str, str],
) -> list[dict[str, str]]:
    """
    Procesa y valida todos los campos de tipo ForeignKey.

    Valida existencia de registros relacionados en base de datos
    para todos los campos que referencian otras entidades.

    Args:
        row: Datos de la fila actual.
        inventory_df: DataFrame completo para referencia.
        row_number: Número de fila para reporte de errores.
        inventory_data: Diccionario donde almacenar datos válidos.

    Returns:
        list: Lista de errores encontrados en campos ForeignKey.
    """
    errors = []
    fk_configs = [
        (InventoryFields.BASE_CITY.value, "base_city", BaseCity, True),
        (
            InventoryFields.BASE_HEADQUARTERS.value,
            "base_headquarters",
            BaseHeadquarters,
            True,
        ),
        (InventoryFields.COMPANY.value, "company", Company, True),
        (InventoryFields.TYPE_DEVICE.value, "type_device", TypeDevice, True),
        (InventoryFields.MANUFACTURER.value, "manufacturer", Manufacturer, True),
        (InventoryFields.STATUS.value, "status", StatusDevice, True),
    ]
    for excel_field, model_field, model_class, required in fk_configs:
        args = {
            "row": row,
            "inventory_df": inventory_df,
            "row_number": row_number,
            "excel_field": excel_field,
            "model_field": model_field,
            "model_class": model_class,
            "required": required,
            "inventory_data": inventory_data,
        }
        error = process_single_fk(FKContextData(**args))
        if error:
            errors.append(error)
    modality_errors = process_device_type_logic(
        row, inventory_df, row_number, inventory_data
    )
    errors.extend(modality_errors)
    return errors


def process_single_fk(args: FKContextData) -> dict[str, str] | None:
    """
    Procesa un campo ForeignKey individual con validación de existencia.

    Busca el registro relacionado en base de datos y valida su existencia,
    manejando campos obligatorios y opcionales apropiadamente.

    Args:
        args: Contexto con datos necesarios para validación FK.

    Returns:
        dict | None: Diccionario con error si validación falla,
                    None si campo es válido.
    """
    column_index = args.inventory_df.columns.get_loc(args.excel_field)
    column_letter = get_column_letter(column_index + 1)
    return_message = None
    if not args.row.get(args.excel_field):
        if args.required:
            return_message = {
                "cell": f"{column_letter}{args.row_number}",
                "error": f"{args.excel_field} es requerido",
            }
        return None
    try:
        obj = args.model_class.objects.filter(name=args.row[args.excel_field]).first()
        if obj:
            args.inventory_data[args.model_field] = obj
            return_message = None
        else:
            return_message = {
                "cell": f"{column_letter}{args.row_number}",
                "error": f"No existe {args.excel_field.lower()}: {args.row[args.excel_field]}",
            }
    except DatabaseError as e:
        return_message = {
            "cell": f"{column_letter}{args.row_number}",
            "error": f"Error consultar el campo {args.excel_field}: {str(e)}",
        }
    return return_message


def process_device_type_logic(
    row: dict[str:str],
    inventory_df: pd.DataFrame,
    row_number: int,
    inventory_data: dict[str, str],
) -> list[dict[str, str]]:
    """
    Procesa lógica específica dependiente del tipo de dispositivo.

    Aplica validaciones condicionales basadas en si el dispositivo
    es fijo o móvil, determinando campos requeridos u opcionales.

    Args:
        row: Datos de la fila actual.
        inventory_df: DataFrame completo para contexto.
        row_number: Número de fila para reporte de errores.
        inventory_data: Diccionario con datos del inventario.

    Returns:
        list: Lista de errores encontrados en lógica condicional.
    """
    errors = []
    type_device = inventory_data.get("type_device")
    if not type_device:
        return errors
    modality = type_device.device_class.lower()
    inventory_data["modality"] = modality
    is_fix = modality == "fijo"
    is_mobile = modality == "movil"
    field_data = FieldContextData(
        row=row,
        inventory_df=inventory_df,
        row_number=row_number,
        common_flag=is_fix,
        inventory_data=inventory_data,
    )
    area_error = process_area_field(field_data)
    if area_error:
        errors.append(area_error)
    floor_error = process_floor_field(field_data)
    if floor_error:
        errors.append(floor_error)
    tag_error = process_modality_tag(
        FieldContextData(
            row=row,
            inventory_df=inventory_df,
            row_number=row_number,
            common_flag=is_mobile,
            inventory_data=inventory_data,
        )
    )
    if tag_error:
        errors.append(tag_error)
    return errors


def process_fields(
    row: dict[str:str],
    inventory_df: pd.DataFrame,
    row_number: int,
    inventory_data: dict[str, str],
    action: str,
) -> tuple[bool, list[dict[str, str]]]:
    """
    Procesa campos requeridos, opcionales y validaciones de duplicados.

    Maneja tres categorías de campos: obligatorios (con validación),
    opcionales (sin validación) y campos únicos (con validación de duplicados).

    Args:
        row: Datos de la fila actual.
        inventory_df: DataFrame para contexto y referencias.
        row_number: Número de fila para reporte de errores.
        inventory_data: Diccionario donde almacenar datos válidos.

    Returns:
        list: Lista de errores encontrados en validaciones de campos.
    """
    errors = []
    update_data = False
    args = {
        "row": row,
        "inventory_df": inventory_df,
        "row_number": row_number,
    }
    fields_required = {}
    fields_duplicated = {}
    if action == "create":
        fields_required = {
            InventoryFields.DOCUMENT.value: "document",
            InventoryFields.USERNAME.value: "username",
            InventoryFields.MODEL.value: "model",
            InventoryFields.SERIAL.value: "serial",
            InventoryFields.OWNERSHIP.value: "ownership",
        }
        fields_optional = {
            InventoryFields.LOCATION.value: "location",
            InventoryFields.NETWORK_USER.value: "network_user",
            InventoryFields.COMMENTS.value: "comments",
            InventoryFields.OS.value: "os",
            InventoryFields.PROCESSOR_AND_SPEED.value: "processor_and_speed",
            InventoryFields.HARD_DRIVE.value: "hard_drive",
            InventoryFields.MEMORY.value: "memory",
            InventoryFields.TOTAL_MEMORY_BANKS.value: "total_memory_banks",
            InventoryFields.USED_BANKS.value: "used_banks",
            InventoryFields.FREE_BANKS.value: "free_banks",
            InventoryFields.LICENSE_PLATE.value: "license_plate",
            InventoryFields.MACHINE_NAME.value: "machine_name",
        }
        fields_duplicated = {
            InventoryFields.SERIAL.value: "serial",
            InventoryFields.MACHINE_NAME.value: "machine_name",
            InventoryFields.LICENSE_PLATE.value: "license_plate",
        }

        for excel_field, model_field in fields_optional.items():
            inventory_data[model_field] = row.get(excel_field) or ""

    if action == "delete":
        errors = []
        fields_required = {
            InventoryFields.SERIAL.value: "serial",
            InventoryFields.DELETE_CAUSE.value: "delete_cause",
        }
        fields_duplicated = {
            InventoryFields.SERIAL.value: "serial",
        }
        inventory_data["is_delete"] = True
    for excel_field, model_field in fields_required.items():
        error = validate_required_field(
            inventory_data,
            ValidationContextData(
                **args,
                excel_field=excel_field,
                model_field=model_field,
            ),
        )
        if error:
            errors.append(error)
    for excel_field, model_field in fields_duplicated.items():
        error = validate_duplicate_field(
            ValidationContextData(
                **args,
                excel_field=excel_field,
                model_field=model_field,
                to_update=update_data,
                action=action,
            )
        )
        if isinstance(error, dict):
            errors.append(error)
        if isinstance(error, Inventory):
            update_data = True
            inventory_data["id"] = error.id
            inventory_data["updated_at"] = timezone.now()

    return update_data, errors


def validate_required_field(
    inventory_data: dict[str, str], args: ValidationContextData
) -> dict[str, str] | None:
    """
    Valida que un campo requerido contenga valor válido.

    Args:
        inventory_data: Diccionario donde almacenar datos válidos.
        args: Contexto con información del campo a validar.

    Returns:
        dict | None: Error si campo requerido está vacío, None si es válido.
    """
    column_index = args.inventory_df.columns.get_loc(args.excel_field)
    column_letter = get_column_letter(column_index + 1)
    if args.row.get(args.excel_field) is not None:
        inventory_data[args.model_field] = args.row[args.excel_field]
        return None
    return {
        "cell": f"{column_letter}{args.row_number}",
        "error": f"{args.excel_field} es requerido",
    }


def validate_duplicate_field(
    args: ValidationContextData,
) -> dict[str, str] | Inventory | None:
    """
    Valida que un campo no esté duplicado en el excel.

    Verifica campos que deben ser únicos en el sistema para
    prevenir duplicación de registros.

    Args:
        args: Contexto con información del campo a validar.

    Returns:
        dict | bool: Error si valor ya existe en el archivo, True si se debe actualizar.
    """
    return_value = None
    action = args.action
    if action == "create":
        return_value = validate_duplicate_field_create(args)
    if action == "delete":
        return_value = validate_duplicate_field_delete(args)
    return return_value


def validate_duplicate_field_create(
    args: ValidationContextData,
) -> dict[str, str] | Inventory | None:
    """
    Valida que un campo no esté duplicado en el excel para actualizar o crear dispositivos.

    Verifica campos que deben ser únicos en el sistema para
    prevenir duplicación de registros.

    Args:
        args: Contexto con información del campo a validar.

    Returns:
        dict | bool: Error si valor ya existe en el archivo, True si se debe actualizar.
    """
    return_value = None
    column_index = args.inventory_df.columns.get_loc(args.excel_field)
    column_letter = get_column_letter(column_index + 1)
    field_value = args.row.get(args.excel_field)
    device = None
    to_update = args.to_update
    if field_value and field_value != "" and not to_update:
        filter_fields = {args.model_field: field_value}
        device = Inventory.objects.filter(**filter_fields).first()
        count_of_device = (
            args.inventory_df[args.excel_field].value_counts().get(field_value, 0)
        )
        if device and args.excel_field == InventoryFields.SERIAL.value:
            to_update = True
        if not to_update or count_of_device > 1:
            if count_of_device > 1:
                return_value = {
                    "cell": f"{column_letter}{args.row_number}",
                    "error": f"El dispositivo con {args.excel_field}: {field_value} ya existe en el archivo.",
                }
            elif device and args.excel_field != InventoryFields.SERIAL.value:
                return_value = {
                    "cell": f"{column_letter}{args.row_number}",
                    "error": f"El dispositivo con {args.excel_field}: {field_value} ya existe en  la base de datos.",
                }
        else:
            return_value = device
    return return_value


def validate_duplicate_field_delete(
    args: ValidationContextData,
) -> dict[str, str] | Inventory | None:
    """
    Valida que un serial no esté duplicado en el excel.

    Args:
        args: Contexto con información del campo a validar.

    Returns:
        dict | bool: Error si valor ya existe en el archivo, True si se debe actualizar.
    """
    return_value = None
    column_index = args.inventory_df.columns.get_loc(args.excel_field)
    column_letter = get_column_letter(column_index + 1)
    field_value = args.row.get(args.excel_field)
    count_of_device = (
        args.inventory_df[args.excel_field].value_counts().get(field_value, 0)
    )
    filter_fields = {args.model_field: field_value}
    return_value = Inventory.objects.filter(**filter_fields).first()
    if field_value and field_value != "" and count_of_device > 1:
        return_value = {
            "cell": f"{column_letter}{args.row_number}",
            "error": f"El dispositivo con {args.excel_field}: {field_value} ya existe en el archivo.",
        }
    return return_value


def process_dates(
    row: dict[str:str],
    inventory_df: pd.DataFrame,
    row_number: int,
    inventory_data: dict[str, str],
) -> list[dict[str, str]]:
    """
    Procesa y valida todos los campos de fecha del inventario.

    Maneja múltiples formatos de fecha y convierte valores de Excel
    (números de serie o cadenas) a objetos date de Python.

    Args:
        row: Datos de la fila actual.
        inventory_df: DataFrame para contexto y referencias.
        row_number: Número de fila para reporte de errores.
        inventory_data: Diccionario donde almacenar fechas válidas.

    Returns:
        list: Lista de errores encontrados en validaciones de fechas.
    """
    errors = []
    date_fields = {
        InventoryFields.WAREHOUSE_ENTRY_DATE.value: "warehouse_entry_date",
        InventoryFields.WARRANTY_DATE.value: "warranty_date",
        InventoryFields.TECHNOLOGY_RENEWAL_DATE.value: "technology_renewal_date",
    }
    for excel_field, model_field in date_fields.items():
        args = ValidationContextData(
            row=row,
            inventory_df=inventory_df,
            row_number=row_number,
            excel_field=excel_field,
            model_field=model_field,
        )
        error = process_single_date(inventory_data, args)
        if error:
            errors.append(error)
    return errors


def process_single_date(
    inventory_data: dict[str, str], args: ValidationContextData
) -> dict[str, str] | None:
    """
    Procesa un campo de fecha individual con múltiples formatos.

    Maneja fechas como números de serie de Excel o cadenas de texto,
    realizando conversión segura con manejo de errores.

    Args:
        inventory_data: Diccionario donde almacenar fecha válida.
        args: Contexto con información del campo de fecha.

    Returns:
        dict | None: Error si formato es inválido, None si fecha es válida.
    """
    column_index = args.inventory_df.columns.get_loc(args.excel_field)
    column_letter = get_column_letter(column_index + 1)
    return_message = None
    date_value = None
    if args.row.get(args.excel_field) is None:
        return_message = {
            "cell": f"{column_letter}{args.row_number}",
            "error": f"{args.excel_field} es requerido",
        }
    elif isinstance(args.row[args.excel_field], float):
        date_value = pd.to_datetime(args.row[args.excel_field], errors="coerce")
    else:
        date_value = pd.to_datetime(args.row[args.excel_field], errors="coerce")
    if not pd.isna(date_value):
        inventory_data[args.model_field] = date_value.date()
    else:
        return_message = {
            "cell": f"{column_letter}{args.row_number}",
            "error": f"Formato de fecha inválido para el campo {args.excel_field}",
        }
    return return_message
