"""
ViewSet base para entidades maestras del sistema CMDB.

Este módulo proporciona una clase base reutilizable para ViewSets que manejan
entidades maestras con funcionalidad común de gestión de estado y eliminación
lógica, estandarizando el comportamiento de las APIs REST del sistema.

Components:
    - CommonManagedFieldsViewSet: ViewSet base abstracto para entidades maestras
"""

from rest_framework.permissions import IsAuthenticated
from rest_framework import viewsets
from django_filters.rest_framework import DjangoFilterBackend


class CommonManagedFieldsViewSet(viewsets.ModelViewSet):
    """
    ViewSet base para entidades con campos administrados comunes.

    Proporciona funcionalidad base para modelos que incluyen campos
    de estado (status) y eliminación lógica (is_delete).

    Permissions:
        IsAuthenticated: Requiere usuario autenticado.

    Filters:
        - status: Filtro por estado activo/inactivo
        - is_delete: Filtro por eliminación lógica
    """

    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["status", "is_delete"]
    pagination_class = None

    class Meta:
        abstract = True
