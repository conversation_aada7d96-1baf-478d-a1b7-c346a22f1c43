"""
Módulo de vistas para la gestión de usuarios, catálogos maestros e inventario.

Este módulo contiene las vistas API para autenticación JWT personalizada

Classes:
    TokenCodeObtainPairView: Vista para obtención de tokens JWT personalizados.
"""

from rest_framework_simplejwt.views import TokenViewBase
from cmdb.jwt.custom_jwt_serializer import CustomCodeObtainPairSerializer


class TokenCodeObtainPairView(TokenViewBase):
    """
    Vista para obtención de token JWT con serializador personalizado.

    Utiliza un serializador customizado para generar tokens de acceso
    con información adicional del usuario.
    """

    serializer_class = CustomCodeObtainPairSerializer
