"""
Módulo de vistas para la gestión de catálogos maestros.

Este módulo contiene las vistas API para el CRUD de catálogos maestros (ciudades, sedes, compañías, etc.)

Classes:
    CommonManagedFieldsViewSet: ViewSet base abstracto para catálogos con campos comunes.
    TypeDeviceViewSet: ViewSet para tipos de dispositivos.
    BaseCityViewSet: ViewSet para ciudades base.
    BaseHeadquartersViewSet: ViewSet para sedes principales.
    CompanyViewSet: ViewSet para compañías.
    AreaViewSet: ViewSet para áreas organizacionales.
    ModalityTagViewSet: ViewSet para etiquetas de modalidad.
    ManufacturerViewSet: ViewSet para fabricantes.
    StatusDeviceViewSet: ViewSet para estados de dispositivos.
"""

from app.models import TypeDevice, BaseCity, BaseHeadquarters, Company
from app.models import Area, ModalityTag, Manufacturer, StatusDevice
from app.serializers import (
    TypeDeviceSerializer,
    BaseCitySerializer,
)
from app.serializers import (
    BaseHeadquartersSerializer,
    CompanySerializer,
    AreaSerializer,
)
from app.serializers import (
    ModalityTagSerializer,
    ManufacturerSerializer,
    StatusDeviceSerializer,
)
from app.views.common_views import CommonManagedFieldsViewSet


class TypeDeviceViewSet(CommonManagedFieldsViewSet):
    """
    ViewSet para gestión de tipos de dispositivos.

    Administra los diferentes tipos de dispositivos disponibles
    en el sistema (computadores, tablets, móviles, etc.).
    """

    queryset = TypeDevice.objects.all()
    serializer_class = TypeDeviceSerializer


class BaseCityViewSet(CommonManagedFieldsViewSet):
    """
    ViewSet para gestión de ciudades base.

    Administra el catálogo de ciudades donde se encuentran
    ubicados los dispositivos del inventario.
    """

    queryset = BaseCity.objects.all()
    serializer_class = BaseCitySerializer


class BaseHeadquartersViewSet(CommonManagedFieldsViewSet):
    """
    ViewSet para gestión de sedes base.

    Administra el catálogo de sedes o sucursales donde
    se encuentran ubicados los dispositivos.
    """

    queryset = BaseHeadquarters.objects.all()
    serializer_class = BaseHeadquartersSerializer


class CompanyViewSet(CommonManagedFieldsViewSet):
    """
    ViewSet para gestión de compañías.

    Administra el catálogo de empresas o compañías
    asociadas a los dispositivos del inventario.
    """

    queryset = Company.objects.all()
    serializer_class = CompanySerializer


class AreaViewSet(CommonManagedFieldsViewSet):
    """
    ViewSet para gestión de áreas organizacionales.

    Administra el catálogo de áreas o departamentos
    donde se asignan los dispositivos fijos.
    """

    queryset = Area.objects.all()
    serializer_class = AreaSerializer


class ModalityTagViewSet(CommonManagedFieldsViewSet):
    """
    ViewSet para gestión de etiquetas de modalidad.

    Administra las etiquetas que clasifican los dispositivos
    móviles según su modalidad de uso.
    """

    queryset = ModalityTag.objects.all()
    serializer_class = ModalityTagSerializer


class ManufacturerViewSet(CommonManagedFieldsViewSet):
    """
    ViewSet para gestión de fabricantes.

    Administra el catálogo de fabricantes o marcas
    de los dispositivos en el inventario.
    """

    queryset = Manufacturer.objects.all()
    serializer_class = ManufacturerSerializer


class StatusDeviceViewSet(CommonManagedFieldsViewSet):
    """
    ViewSet para gestión de estados de dispositivos.

    Administra los diferentes estados que pueden tener
    los dispositivos (activo, inactivo, en reparación, etc.).
    """

    queryset = StatusDevice.objects.all()
    serializer_class = StatusDeviceSerializer
