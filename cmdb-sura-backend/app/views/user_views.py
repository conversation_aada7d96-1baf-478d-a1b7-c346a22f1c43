"""
Módulo de vistas para la gestión de usuarios.

Este módulo contiene las vistas API para la gestión de usuarios con permisos administrativos

Classes:
    UserViewSet: ViewSet para gestión completa de usuarios.
"""

from rest_framework import viewsets, filters
from rest_framework.permissions import IsAdminUser
from django.contrib.auth import get_user_model
from app.serializers import (
    UserListSerializer,
    UserCreateSerializer,
    UserUpdateSerializer,
)

User = get_user_model()


class UserViewSet(viewsets.ModelViewSet):
    """
    ViewSet para gestión completa de usuarios del sistema.

    Proporciona operaciones CRUD para usuarios con permisos de administrador,
    incluyendo filtrado por búsqueda y ordenamiento por múltiples campos.

    Permissions:
        IsAdminUser: Solo administradores pueden acceder.

    Filters:
        - Búsqueda por username, email, first_name, last_name
        - Ordenamiento por username, date_joined, last_login
    """

    queryset = User.objects.all().order_by("-date_joined")
    permission_classes = [IsAdminUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ["username", "email", "first_name", "last_name"]
    ordering_fields = ["username", "date_joined", "last_login"]
    pagination_class = None

    def get_serializer_class(self) -> type:
        """
        Retorna el serializer apropiado según la acción solicitada.

        Returns:
            type: Clase del serializer correspondiente a la acción:
                - UserListSerializer para listado
                - UserCreateSerializer para creación
                - UserUpdateSerializer para actualización
        """
        action_serializer = None
        match self.action:
            case "list":
                action_serializer = UserListSerializer
            case "create":
                action_serializer = UserCreateSerializer
            case "update" | "partial_update":
                action_serializer = UserUpdateSerializer
        return action_serializer
