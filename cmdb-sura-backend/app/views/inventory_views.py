"""
Módulo de vistas para la gestión de inventario.

Este módulo contiene las vistas API para:
- Gestión de inventario de dispositivos
- Exportación e importación de datos mediante archivos Excel
- Generación de plantillas Excel para carga masiva

Classes:
    InventoryViewSet: ViewSet para gestión de inventario.
    ExportarExcelAPIView: Vista para exportar inventario a Excel.
    GenerateExcelTemplateView: Vista para generar plantillas Excel.
    ExcelImportAPIView: Vista para importar inventario desde Excel.
"""

import logging
import io
import pandas as pd
import polars as pl
import numpy as np
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from djangorestframework_camel_case.parser import (
    CamelCaseJSONParser,
    CamelCaseMultiPartParser,
)
from django.http import HttpResponse
from django.core.files.uploadedfile import UploadedFile
from django.db import (
    DatabaseError,
    transaction,
    IntegrityError,
    OperationalError,
    models,
)
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Protection
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.cell.cell import Cell
from app.models import Inventory, TypeDevice, BaseCity, BaseHeadquarters, Company
from app.models import Area, ModalityTag, Manufacturer, StatusDevice
from app.commons.consts import HEADERS, HEADERS_DELETE, VALIDATION_RANGES
from app.commons.enums import InventoryFields
from app.utils.inventory_utils import (
    get_header_style,
    process_single_row,
)
from app.views.common_views import CommonManagedFieldsViewSet
from app.serializers import InventorySerializer
from app.commons.contexts import HeaderStyle


logger = logging.getLogger("inventoryViews")


class CustomPagination(PageNumberPagination):
    """
    Clase custom utilizando el atributo page_size para
    controlar la paginación desde el cliente.
    """

    page_size_query_param = "page_size"


class InventoryViewSet(CommonManagedFieldsViewSet):
    """
    ViewSet optimizado para gestión del inventario de dispositivos.

    Permite operaciones CRUD sobre el inventario con filtrado
    por estado del dispositivo y consultas optimizadas.

    Optimizaciones implementadas:
        - select_related para relaciones FK simples
        - prefetch_related para relaciones complejas si las hay
        - Reduce consultas de N+1 a consultas constantes
        - Mejora significativa en tiempos de respuesta
    """

    serializer_class = InventorySerializer
    filterset_fields = ["status"]
    pagination_class = CustomPagination
    queryset = Inventory.objects.select_related(
        "base_city",
        "base_headquarters",
        "status",
        "type_device",
        "company",
        "manufacturer",
        "area",
        "modality_tag",
    )

    def get_queryset(self) -> models.QuerySet:
        """
        Optimiza el queryset con select_related para evitar consultas N+1.

        Hereda el queryset base optimizado y permite personalizaciones
        adicionales según el contexto (filtros, usuario, etc.).
        """

        queryset = super().get_queryset()
        try:
            is_deleted = self.request.query_params["is_deleted"] == "true"
        except KeyError:
            is_deleted = False
        try:
            filter_text = self.request.query_params["search"]
        except KeyError:
            filter_text = ""
        if filter_text:
            queryset = queryset.filter(is_delete=is_deleted).filter(
                models.Q(serial__icontains=filter_text)
                | models.Q(license_plate__icontains=filter_text)
                | models.Q(machine_name__icontains=filter_text)
            ).order_by("serial")
        else:
            queryset = queryset.all().filter(is_delete=is_deleted).order_by("serial")
        return queryset


class ExportarExcelAPIView(APIView):
    """
    Vista para exportar el inventario completo a archivo Excel.

    Genera un archivo Excel con todos los registros del inventario,
    incluyendo información detallada de dispositivos, ubicaciones,
    especificaciones técnicas y fechas relevantes.

    Methods:
        GET: Descarga archivo Excel con inventario completo.

    Returns:
        HttpResponse: Archivo Excel con extensión .xlsx listo para descarga.
    """
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.action = None

    def get(self, request: Request) -> HttpResponse:
        """
        Genera y retorna archivo Excel con el inventario completo.
        """
        self.action = request.GET.get("action")
        data = self._create_dataframe()
        output = self._export_to_excel(data)
        return self._create_response(output)

    def _create_dataframe(self) -> pl.DataFrame:
        """Crea DataFrame con los datos del inventario."""
        queryset = self._get_queryset()
        data_list = [self._serialize_item(item) for item in queryset]
        data = pl.DataFrame(data_list)
        if self.action == "delete":
            headers = HEADERS + ["causal_de_baja"]
        else:
            headers = HEADERS
        column_map = dict(zip(data.columns, headers))
        return data.rename(column_map).select(headers)

    def _get_queryset(self) -> models.QuerySet:
        """Obtiene queryset optimizado con relaciones."""
        if self.action == "delete":
            queryset = Inventory.objects.select_related(
                "base_city",
                "base_headquarters",
                "status",
                "type_device",
                "company",
                "manufacturer",
                "area",
                "modality_tag",
            ).filter(is_delete=True)
        else:
            queryset = Inventory.objects.select_related(
                "base_city",
                "base_headquarters",
                "status",
                "type_device",
                "company",
                "manufacturer",
                "area",
                "modality_tag",
            ).all().filter(is_delete=False)
        return queryset

    def _serialize_item(self, item: Inventory) -> dict[str, str]:
        """Serializa un item del inventario a diccionario."""
        return {
            "ciudad_base": self._get_related_name(item, "base_city"),
            "sede_base": self._get_related_name(item, "base_headquarters"),
            "ubicacion": item.location,
            "cedula": item.document,
            "compania": self._get_related_name(item, "company"),
            "nombre_de_usuario": item.username,
            "tipo_de_dispositivo": self._get_related_name(item, "type_device"),
            "area": self._get_related_name(item, "area"),
            "piso": item.floor,
            "fabricante": self._get_related_name(item, "manufacturer"),
            "modelo": item.model,
            "serial": item.serial,
            "placa": item.license_plate,
            "propiedad": item.ownership,
            "fecha_de_ingreso_a_bodega": item.warehouse_entry_date,
            "usuario_de_red": item.network_user,
            "modalidad": item.modality,
            "etiqueta_de_modalidad": self._get_related_name(item, "modality_tag"),
            "observaciones": item.comments,
            "estado_de_dispositivo": self._get_related_name(item, "status"),
            "nombre_de_maquina": item.machine_name,
            "fecha_de_garantia": item.warranty_date,
            "so": item.os,
            "procesador_y_velocidad": item.processor_and_speed,
            "disco_duro": item.hard_drive,
            "memoria": item.memory,
            "total_bancos_de_memoria": item.total_memory_banks,
            "bancos_usados": item.used_banks,
            "bancos_libres": item.free_banks,
            "fecha_de_renovacion_tecnologica": item.technology_renewal_date,
            "causal_de_baja": item.delete_cause,
        }

    def _get_related_name(self, item: Inventory, field_name: str) -> str:
        """Obtiene el nombre de un campo relacionado o cadena vacía."""
        related_obj = getattr(item, field_name, None)
        return related_obj.name if related_obj else ""

    def _export_to_excel(self, data: pl.DataFrame) -> io.BytesIO:
        """Procesa fechas y exporta DataFrame a Excel."""
        data = self._remove_timezones(data)
        output = io.BytesIO()
        data.write_excel(output, worksheet="Inventario")
        return output

    def _remove_timezones(self, data: pl.DataFrame) -> pl.DataFrame:
        """Remueve timezone de todas las columnas datetime."""
        datetime_cols = [col for col in data.columns if data[col].dtype == pl.Datetime]
        for col in datetime_cols:
            data = data.with_columns(pl.col(col).dt.replace_time_zone(None).alias(col))
        return data

    def _create_response(self, output: io.BytesIO) -> HttpResponse:
        """Crea la respuesta HTTP con el archivo Excel."""
        return HttpResponse(
            output.getvalue(),
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": 'attachment; filename="Inventory.xlsx"'},
        )


class GenerateExcelTemplateView(APIView):
    """
    Vista para generar plantilla Excel para carga masiva de inventario.

    Crea un archivo Excel con validaciones, listas desplegables, formatos
    de fecha y fórmulas automáticas para facilitar la carga masiva de
    datos de inventario.

    Features:
        - Encabezados
        - Listas desplegables con datos del sistema
        - Validaciones de datos automáticas
        - Formatos de fecha predefinidos
        - Cálculos automáticos de fechas de renovación
        - Hoja oculta con datos de referencia

    Methods:
        GET: Descarga plantilla Excel configurada.
    """
    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.action = None

    def get(self, request: Request) -> HttpResponse:
        """
        Genera y retorna plantilla Excel para carga de inventario.

        Construye un archivo Excel con dos hojas: una principal para
        entrada de datos con validaciones y una oculta con listas
        de valores permitidos.

        Args:
            request: Objeto Request de Django REST Framework.

        Returns:
            HttpResponse: Respuesta HTTP con plantilla Excel lista
                         para descarga y uso en carga masiva.
        """
        work_book = Workbook()
        work_sheet = work_book.active
        work_sheet.title = "Plantilla Inventario"
        self.action = request.GET.get("action")
        self._setup_headers(work_sheet)
        if self.action == "create":
            hidden_work_sheet = work_book.create_sheet(title="ListasValores")
            self._setup_data_validations(work_sheet, hidden_work_sheet)
            self._setup_date_formats(work_sheet)
            self._setup_dynamic_formulas(work_sheet)
            hidden_work_sheet.sheet_state = "hidden"
        return self._create_response(work_book)

    def _setup_headers(self, work_sheet: Worksheet) -> None:
        """
        Configura los encabezados de la hoja con formato profesional.

        Aplica estilo visual consistente a todos los encabezados incluyendo
        fuente, colores, alineación y bordes.

        Args:
            work_sheet: Hoja de cálculo donde configurar los encabezados.
        """
        if self.action == "delete":
            headers = HEADERS_DELETE
        else:
            headers = HEADERS
        header_style = get_header_style()
        for col_num, header in enumerate(headers, 1):
            cell = work_sheet.cell(row=1, column=col_num, value=header)
            self._apply_header_style(cell, header_style)
            work_sheet.column_dimensions[get_column_letter(col_num)].width = 20

    def _apply_header_style(self, cell: Cell, style: HeaderStyle) -> None:
        """
        Aplica el estilo definido a una celda de encabezado específica.

        Args:
            cell: Celda de Excel donde aplicar el estilo.
            style: Objeto con la configuración de estilo.
        """
        cell.font = style.font
        cell.fill = style.fill
        cell.alignment = style.alignment
        cell.border = style.border

    def _setup_data_validations(
        self, work_sheet: Worksheet, hidden_work_sheet: Worksheet
    ) -> None:
        """
        Configura todas las validaciones de datos para campos con listas.

        Crea listas desplegables para campos que requieren selección de
        valores predefinidos del sistema, mejorando la calidad de datos.

        Args:
            work_sheet: Hoja principal donde aplicar validaciones.
            hidden_work_sheet: Hoja oculta donde almacenar listas de valores.
        """
        validation_data = self._get_validation_data()
        validations = self._create_basic_validations(validation_data, hidden_work_sheet)
        self._apply_validations_to_worksheet(work_sheet, validations)

    def _get_validation_data(self) -> dict[str, list[str]]:
        """
        Obtiene todos los datos necesarios para crear validaciones.

        Consulta la base de datos para obtener valores activos de todas
        las entidades relacionadas con el inventario.

        Returns:
            dict: Diccionario con listas de valores para cada tipo de validación,
                 filtrados por estado activo y no eliminados.
        """
        return {
            "cities": list(
                BaseCity.objects.filter(status=True, is_delete=False).values_list(
                    "name", flat=True
                )
            ),
            "headquarters": list(
                BaseHeadquarters.objects.filter(
                    status=True, is_delete=False
                ).values_list("name", flat=True)
            ),
            "companies": list(
                Company.objects.filter(status=True, is_delete=False).values_list(
                    "name", flat=True
                )
            ),
            "type_devices": list(
                TypeDevice.objects.filter(status=True, is_delete=False).values_list(
                    "name", flat=True
                )
            ),
            "areas": list(
                Area.objects.filter(status=True, is_delete=False).values_list(
                    "name", flat=True
                )
            ),
            "modality_tags": list(
                ModalityTag.objects.filter(status=True, is_delete=False).values_list(
                    "name", flat=True
                )
            ),
            "manufacturers": list(
                Manufacturer.objects.filter(status=True, is_delete=False).values_list(
                    "name", flat=True
                )
            ),
            "statuses": list(
                StatusDevice.objects.filter(status=True, is_delete=False).values_list(
                    "name", flat=True
                )
            ),
        }

    def _create_basic_validations(
        self, validation_data: dict[str, list[str]], hidden_work_sheet: Worksheet
    ) -> dict[str, DataValidation]:
        """
        Crea las validaciones básicas de datos para campos obligatorios.

        Configura listas desplegables para los campos principales del
        inventario utilizando datos de la base de datos.

        Args:
            validation_data: Diccionario con listas de valores permitidos.
            hidden_work_sheet: Hoja donde almacenar las listas de referencia.

        Returns:
            dict: Diccionario con objetos DataValidation configurados
                 para cada campo que requiere validación.
        """
        validation_configs = [
            ("A", InventoryFields.BASE_CITY.value, validation_data["cities"]),
            (
                "B",
                InventoryFields.BASE_HEADQUARTERS.value,
                validation_data["headquarters"],
            ),
            ("C", InventoryFields.COMPANY.value, validation_data["companies"]),
            ("D", InventoryFields.TYPE_DEVICE.value, validation_data["type_devices"]),
            ("E", InventoryFields.AREA.value, validation_data["areas"]),
            ("F", InventoryFields.MANUFACTURER.value, validation_data["manufacturers"]),
            ("G", InventoryFields.STATUS.value, validation_data["statuses"]),
            ("H", InventoryFields.MODALITY_TAG.value, validation_data["modality_tags"]),
        ]
        validations = {}
        for column, field_name, values in validation_configs:
            validations[column] = self._add_dropdown_list(
                column, field_name, values, hidden_work_sheet
            )
        return validations

    def _apply_validations_to_worksheet(
        self, work_sheet: Worksheet, validations: dict[str, DataValidation]
    ) -> None:
        """
        Aplica las validaciones configuradas a los rangos de celdas apropiados.

        Asigna cada validación a su columna correspondiente en un rango
        extendido para permitir múltiples registros.

        Args:
            work_sheet: Hoja donde aplicar las validaciones.
            validations: Diccionario con validaciones configuradas.
        """

        for column, cell_range in VALIDATION_RANGES.items():
            if column in validations:
                work_sheet.add_data_validation(validations[column])
                validations[column].add(cell_range)

    def _setup_date_formats(self, work_sheet: Worksheet) -> None:
        """
        Configura el formato de fecha y resaltado para campos temporales.

        Aplica formato DD/MM/YYYY y color de fondo distintivo a todas
        las columnas que contienen fechas.

        Args:
            work_sheet: Hoja donde configurar formatos de fecha.
        """
        date_format = "DD/MM/YYYY"
        date_fill = PatternFill(
            start_color="E6F2FF", end_color="E6F2FF", fill_type="solid"
        )
        date_columns = [15, 22, 30]

        for row in range(2, 1001):
            for col in date_columns:
                cell = work_sheet.cell(row=row, column=col)
                cell.number_format = date_format
                cell.fill = date_fill

    def _setup_dynamic_formulas(self, work_sheet: Worksheet) -> None:
        """
        Configura fórmulas dinámicas para cálculos automáticos.

        Establece fórmulas que calculan automáticamente la fecha de
        renovación tecnológica y la modalidad del dispositivo basándose
        en el tipo seleccionado.

        Args:
            work_sheet: Hoja donde configurar las fórmulas automáticas.
        """
        headers = HEADERS
        column_indices = self._get_formula_column_indices(headers)
        device_data = self._get_device_type_data()

        for row in range(2, 1001):
            self._apply_row_formulas(work_sheet, row, column_indices, device_data)

    def _get_formula_column_indices(self, headers: list[str]) -> dict[str, int]:
        """
        Obtiene los índices de columnas necesarias para las fórmulas automáticas.

        Args:
            headers: Lista de encabezados de la plantilla.

        Returns:
            dict: Diccionario con índices de columnas relevantes para fórmulas.
        """
        return {
            "type_device": headers.index(InventoryFields.TYPE_DEVICE.value) + 1,
            "warehouse_entry_date": headers.index(
                InventoryFields.WAREHOUSE_ENTRY_DATE.value
            )
            + 1,
            "technology_renewal_date": headers.index(
                InventoryFields.TECHNOLOGY_RENEWAL_DATE.value
            )
            + 1,
            "device_class": headers.index(InventoryFields.MODALITY.value) + 1,
        }

    def _get_device_type_data(self) -> dict[str, list[str]]:
        """
        Obtiene datos de tipos de dispositivo para generar fórmulas dinámicas.

        Returns:
            dict: Diccionario con nombres, años de vida útil y clases
                 de todos los tipos de dispositivo activos.
        """
        devices = TypeDevice.objects.filter(status=True, is_delete=False)
        return {
            "names": [device.name for device in devices],
            "years": [device.years for device in devices],
            "classes": [device.device_class for device in devices],
        }

    def _apply_row_formulas(
        self,
        work_sheet: Worksheet,
        row: int,
        column_indices: dict[str, int],
        device_data: dict[str, list[str]],
    ) -> None:
        """
        Aplica fórmulas automáticas a una fila específica.

        Configura fórmulas para calcular fecha de renovación y modalidad
        del dispositivo, protegiendo las celdas contra edición manual.

        Args:
            work_sheet: Hoja donde aplicar las fórmulas.
            row: Número de fila donde aplicar las fórmulas.
            column_indices: Índices de columnas relevantes.
            device_data: Datos de tipos de dispositivo para las fórmulas.
        """
        cells = self._build_cell_references(row, column_indices)
        renewal_formula = self._build_renewal_date_formula(
            cells["type_device"], cells["warehouse_entry_date"], device_data
        )
        work_sheet[cells["technology_renewal_date"]].value = f"={renewal_formula}"
        work_sheet[cells["technology_renewal_date"]].protection = Protection(
            locked=True
        )
        class_formula = self._build_device_class_formula(
            cells["type_device"], device_data
        )
        work_sheet[cells["device_class"]].value = f"={class_formula}"
        work_sheet[cells["device_class"]].protection = Protection(locked=True)

    def _build_cell_references(
        self, row: int, column_indices: dict[str, int]
    ) -> dict[str, str]:
        """
        Construye referencias de celdas en formato Excel para una fila específica.

        Args:
            row: Número de fila para generar referencias.
            column_indices: Índices de columnas a referenciar.

        Returns:
            dict: Diccionario con referencias de celdas en formato Excel (ej: A2, B3).
        """
        return {
            "type_device": f"{get_column_letter(column_indices['type_device'])}{row}",
            "warehouse_entry_date": f"{get_column_letter(column_indices['warehouse_entry_date'])}{row}",
            "technology_renewal_date": f"{get_column_letter(column_indices['technology_renewal_date'])}{row}",
            "device_class": f"{get_column_letter(column_indices['device_class'])}{row}",
        }

    def _build_renewal_date_formula(
        self,
        type_device_cell: str,
        entry_date_cell: str,
        device_data: dict[str, list[str]],
    ) -> str:
        """
        Construye fórmula Excel para calcular fecha de renovación tecnológica.

        Crea una fórmula IF anidada que suma años de vida útil a la fecha
        de ingreso según el tipo de dispositivo seleccionado.

        Args:
            type_device_cell: Referencia de celda del tipo de dispositivo.
            entry_date_cell: Referencia de celda de fecha de ingreso.
            device_data: Datos de tipos de dispositivo con años de vida útil.

        Returns:
            str: Fórmula Excel completa para calcular fecha de renovación.
        """
        formula_parts = []
        for i, device_name in enumerate(device_data["names"]):
            years = device_data["years"][i]
            formula_parts.append(
                f'IF({type_device_cell}="{device_name}",'
                f"DATE(YEAR({entry_date_cell})+{years},"
                f"MONTH({entry_date_cell}),DAY({entry_date_cell}))"
            )
        formula = formula_parts[0]
        for part in formula_parts[1:]:
            formula += f",{part}"
        formula += ',"")' + ")" * (len(device_data["names"]) - 1)
        return formula

    def _build_device_class_formula(
        self, type_device_cell: str, device_data: dict[str, list[str]]
    ) -> str:
        """
        Construye fórmula Excel para determinar clase de dispositivo automáticamente.

        Crea fórmula IF anidada que asigna la clase (Fijo/Móvil) según
        el tipo de dispositivo seleccionado.

        Args:
            type_device_cell: Referencia de celda del tipo de dispositivo.
            device_data: Datos de tipos de dispositivo con sus clases.

        Returns:
            str: Fórmula Excel completa para determinar clase de dispositivo.
        """
        formula_parts = []
        for i, device_name in enumerate(device_data["names"]):
            device_class = device_data["classes"][i]
            formula_parts.append(
                f'IF({type_device_cell}="{device_name}","{device_class}"'
            )
        formula = formula_parts[0]
        for part in formula_parts[1:]:
            formula += f",{part}"
        formula += ',"")' + ")" * (len(device_data["names"]) - 1)
        return formula

    def _add_dropdown_list(
        self,
        column_letter: str,
        list_name: str,
        values: list[str],
        hidden_work_sheet: Worksheet,
    ) -> DataValidation:
        """
        Crea lista desplegable con validación de datos para una columna.

        Configura una validación que restringe entrada a valores predefinidos,
        almacenando la lista de referencia en hoja oculta.

        Args:
            column_letter: Letra de columna donde aplicar validación.
            list_name: Nombre descriptivo de la lista para mensajes.
            values: Lista de valores permitidos.
            hidden_work_sheet: Hoja oculta donde almacenar valores de referencia.

        Returns:
            DataValidation: Objeto de validación configurado con lista desplegable
                           y mensajes de error personalizados.
        """
        for index, value in enumerate(values, 1):
            hidden_work_sheet.cell(
                row=index, column=ord(column_letter) - 64, value=value
            )

        formula = f"=ListasValores!${column_letter}$1:${column_letter}${len(values)}"
        data_validation = DataValidation(
            type="list", formula1=formula, allow_blank=True
        )

        data_validation.error = "Por favor seleccione un valor de la lista"
        data_validation.errorTitle = "Valor inválido"
        data_validation.prompt = f"Seleccione un {list_name}"
        data_validation.promptTitle = "Lista de selección"
        return data_validation

    def _create_response(self, work_book: Workbook) -> HttpResponse:
        """
        Crea respuesta HTTP con archivo Excel de plantilla configurado.

        Args:
            work_book: Libro de Excel completamente configurado.

        Returns:
            HttpResponse: Respuesta HTTP con archivo Excel adjunto,
                         listo para descarga con nombre predefinido.
        """
        response = HttpResponse(
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        response["Content-Disposition"] = (
            "attachment; filename=plantilla_inventario.xlsx"
        )
        work_book.save(response)
        return response


class ExcelImportAPIView(APIView):
    """
    Vista para importar inventario masivo desde archivo Excel.

    Procesa archivos Excel validando estructura, datos y relaciones,
    realizando carga masiva con manejo detallado de errores por celda.

    Features:
        - Validación de estructura de archivo
        - Validación de encabezados requeridos
        - Procesamiento fila por fila con reporte de errores
        - Validación de campos obligatorios y opcionales
        - Validación de relaciones ForeignKey
        - Validación de duplicados en base de datos
        - Procesamiento de fechas con múltiples formatos
        - Lógica condicional por tipo de dispositivo
        - Transacción atómica para garantizar consistencia

    Methods:
        POST: Procesa archivo Excel y carga datos validados.
    """

    renderer_classes = [CamelCaseJSONRenderer]
    parser_classes = [CamelCaseJSONParser, CamelCaseMultiPartParser]

    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.action = None

    def post(self, request: Request) -> Response:
        """
        Procesa archivo Excel para importación masiva de inventario.

        Valida estructura del archivo, procesa cada fila con validaciones
        completas y realiza carga masiva de registros válidos en transacción
        atómica, proporcionando reporte detallado de errores.

        Args:
            request: Request con archivo Excel en FILES['file'].

        Returns:
            Response: JSON con resultado del procesamiento incluyendo:
                     - success: Booleano indicando éxito general
                     - message: Mensaje descriptivo del resultado
                     - total_processed: Total de filas procesadas
                     - valid_registers: Cantidad de registros válidos
                     - registers_with_errors: Cantidad con errores
                     - errors: Lista detallada de errores por celda
        """
        try:
            response_message = None
            excel_file = request.FILES.get("file")
            self.action = request.data.get("action")
            inventory_df = pd.read_excel(excel_file, dtype=str)
            inventory_df = inventory_df.replace({np.nan: None})
            validation_result = self._validate_file(excel_file)
            if not validation_result:
                header_validation = self._validate_headers(inventory_df)
                if not header_validation:
                    valid_inventories, update_inventories, errors = (
                        self._process_excel_rows(inventory_df)
                    )
                    return self._save_and_respond(
                        valid_inventories,
                        update_inventories,
                        errors,
                        len(inventory_df),
                    )
                response_message = header_validation
            else:
                response_message = validation_result
            return response_message
        except OperationalError as e:
            logger.error("Error al conectar a la base de datos: %s", str(e))
            return Response(
                {
                    "success": False,
                    "message": "Error al procesar el archivo. Por favor contacte al administrador.",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _validate_file(self, excel_file: UploadedFile) -> Response | None:
        """
        Valida archivo Excel antes del procesamiento principal.

        Verifica existencia del archivo, extensión válida y contenido no vacío.

        Args:
            excel_file: Archivo subido por el usuario.

        Returns:
            Response | None: Response con error si validación falla,
                            None si archivo es válido.
        """
        return_message = None
        if not excel_file:
            return_message = Response(
                {"error": "No se proporcionó ningún archivo"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        elif not excel_file.name.endswith((".xlsx", ".xls")):
            return_message = Response(
                {"error": "El archivo debe ser un Excel (.xlsx o .xls)"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        elif pd.read_excel(excel_file, dtype=str).empty:
            return_message = Response(
                {"error": "El archivo Excel no contiene datos"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        return return_message

    def _validate_headers(
        self, inventory_df: pd.DataFrame
    ) -> Response | None:
        """
        Valida que el DataFrame contenga todos los encabezados requeridos.

        Compara columnas del archivo contra encabezados esperados del sistema,
        identificando columnas faltantes para guiar al usuario.

        Args:
            inventory_df: DataFrame de pandas con datos del Excel.

        Returns:
            Response | None: Response con error listando encabezados faltantes,
                            None si todos los encabezados están presentes.
        """
        if self.action == "create":
            expected_headers = HEADERS
        if self.action == "delete":
            expected_headers = HEADERS_DELETE
        missing_headers = [
            header for header in expected_headers if header not in inventory_df.columns
        ]
        if missing_headers:
            return Response(
                {
                    "error": "La plantilla Excel no contiene todos los encabezados requeridos",
                    "missing_headers": missing_headers,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        return None

    def _process_excel_rows(
        self, inventory_df: pd.DataFrame
    ) -> tuple[list[Inventory], list[Inventory], list[dict[str, str]]]:
        """
        Procesa todas las filas del Excel validando datos fila por fila.

        Itera sobre cada fila del DataFrame, validando todos los campos
        y relaciones, separando registros válidos de errores detallados.

        Args:
            inventory_df: DataFrame con datos del inventario.

        Returns:
            tuple: Tupla con lista de objetos Inventory válidos y
                    lista de diccionarios con errores detallados por celda.
        """
        valid_inventories = []
        update_inventories = []
        errors = []
        for index, row in inventory_df.iterrows():
            row_number = index + 2
            update_data, inventory_data, row_errors = process_single_row(
                row, inventory_df, row_number, self.action
            )
            if row_errors:
                errors.extend(row_errors)
            else:
                try:
                    inventory = Inventory(**inventory_data)
                    if update_data:
                        update_inventories.append(inventory)
                    else:
                        valid_inventories.append(inventory)
                except DatabaseError as e:
                    errors.append(
                        {
                            "cell": f"Fila {row_number}",
                            "error": f"Error al crear el objeto Inventory: {str(e)}",
                        }
                    )
        return valid_inventories, update_inventories, errors

    def _save_and_respond(
        self,
        valid_inventories: list[Inventory],
        update_inventories: list[Inventory],
        errors: list[dict[str, str]],
        total_processed: int,
    ) -> Response:
        """
        Guarda inventarios válidos en transacción atómica y genera respuesta.

        Si no hay errores, realiza inserción masiva en transacción atómica.
        Proporciona respuesta detallada con estadísticas y errores específicos.

        Args:
            valid_inventories: Lista de objetos Inventory validados para guardar.
            errors: Lista de errores encontrados durante el procesamiento.
            total_processed: Número total de filas procesadas del archivo.

        Returns:
            Response: JSON con resultado final incluyendo estadísticas completas
                     y lista detallada de errores por celda específica.
        """
        imported_count = len(valid_inventories) + len(update_inventories)
        if len(errors) == 0 and (
            len(valid_inventories) > 0 or len(update_inventories) > 0
        ):
            try:
                with transaction.atomic():
                    if len(valid_inventories) > 0:
                        Inventory.objects.bulk_create(valid_inventories)
                    if len(update_inventories) > 0:
                        fields_to_update = []
                        if self.action == "create":
                            fields_to_update = [
                                field.name
                                for field in Inventory._meta.fields
                                if field.name not in ("id", "created_at")
                            ]
                        elif self.action == "delete":
                            fields_to_update = [
                                "is_delete",
                                "delete_cause",
                                "updated_at",
                            ]
                        Inventory.objects.bulk_update(
                            update_inventories, fields_to_update
                        )
                return Response(
                    {
                        "success": True,
                        "message": f"Se procesaron {imported_count} registros de forma exitosa.",
                        "total_processed": total_processed,
                        "total_updated": len(update_inventories),
                        "total_created": len(valid_inventories),
                    }
                )
            except IntegrityError as e:
                return Response(
                    {
                        "success": False,
                        "message": f"Error al guardar los registros: {str(e)}",
                        "errors": errors,
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
        else:
            return Response(
                {
                    "success": False,
                    "message": "No se importaron los registros debido a algunos errores en la plantilla.",
                    "total_processed": total_processed,
                    "valid_registers": imported_count,
                    "registers_with_errors": len(errors),
                    "errors": errors,
                }
            )
