"""
Serializadores para el sistema de gestión de inventario CMDB.

Este módulo contiene todos los serializadores de Django REST Framework
para la gestión completa del inventario de dispositivos tecnológicos,
usuarios del sistema y entidades maestras de configuración.

Components:
    User Serializers:
        - UserListSerializer: Listado simplificado de usuarios
        - UserCreateSerializer: Creación con validación LDAP integrada
        - UserUpdateSerializer: Actualización de información básica

    Inventory Serializers:
        - InventorySerializer: Gestión completa de dispositivos con campos calculados

    Master Data Serializers:
        - TypeDeviceSerializer: Tipos de dispositivos y configuraciones
        - BaseCitySerializer: Catálogo de ciudades
        - BaseHeadquartersSerializer: Sedes y sucursales
        - CompanySerializer: Empresas y organizaciones
        - AreaSerializer: Departamentos y áreas funcionales
        - ModalityTagSerializer: Etiquetas para dispositivos móviles
        - ManufacturerSerializer: Fabricantes y marcas
        - StatusDeviceSerializer: Estados del ciclo de vida

    Utilities:
        - AllFieldsMeta: Mixin para configuración de campos completos

Features:
    - Integración con sistema LDAP corporativo para usuarios
    - Campos calculados dinámicamente para optimización
    - Validaciones personalizadas para integridad de datos
    - Generación automática de contraseñas seguras
    - Serialización optimizada con campos de solo lectura
    - Soporte completo para operaciones CRUD

Security:
    - Validación obligatoria contra LDAP para nuevos usuarios
    - Generación segura de contraseñas temporales
    - Campos de solo lectura para prevenir manipulación
    - Validación de integridad en relaciones FK

Performance:
    - Campos ReadOnlyField para reducir consultas
    - SerializerMethodField para cálculos dinámicos eficientes
    - Reutilización de configuraciones con AllFieldsMeta

Dependencies:
    - rest_framework: Framework base para serialización
    - django.contrib.auth: Modelo de usuario de Django
    - app.models: Modelos de dominio del inventario
    - cmdb.ldap: Integración con directorio corporativo
    - secrets: Generación segura de contraseñas

Usage:
    Los serializers se utilizan principalmente en ViewSets para:
    - APIs REST de gestión de inventario
    - Validación de datos en formularios
    - Transformación de datos para frontend
    - Exportación/importación de datos

    Example:
        # Crear usuario con validación LDAP
        serializer = UserCreateSerializer(data={'username': 'jdoe'})
        if serializer.is_valid():
            user = serializer.save()

        # Serializar inventario con campos calculados
        inventory = Inventory.objects.get(pk=1)
        serializer = InventorySerializer(inventory)
        data = serializer.data  # Incluye campos como type_device_icon
"""

import string
import secrets
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from django.contrib.auth import get_user_model
from app.commons.contexts import CommonMeta
from app.models import Inventory, TypeDevice, BaseCity, BaseHeadquarters
from app.models import Company, Area, ModalityTag, Manufacturer, StatusDevice
from cmdb.ldap.connection_ldap import Ldap

User = get_user_model()


class UserListSerializer(serializers.ModelSerializer):
    """
    Serializer para listar usuarios del sistema con información básica.

    Proporciona una vista simplificada de usuarios incluyendo solo
    los campos esenciales para listados y tablas de datos.

    Fields:
        id: Identificador único del usuario
        username: Nombre de usuario único
        is_active: Estado activo/inactivo del usuario
        is_staff: Privilegios de staff del usuario
    """

    class Meta:
        model = User
        fields = ["id", "username", "is_active", "is_staff"]


class UserCreateSerializer(serializers.ModelSerializer):
    """
    Serializer para crear usuarios con validación LDAP integrada.

    Valida la existencia del usuario en el sistema LDAP corporativo
    antes de crear la cuenta, extrayendo información personal automáticamente
    y generando contraseña segura aleatoria.

    Features:
        - Validación obligatoria contra LDAP
        - Extracción automática de datos personales (email, nombres)
        - Generación de contraseña segura automática
        - Configuración de permisos básicos

    Fields:
        username: Nombre de usuario que debe existir en LDAP
        is_active: Estado inicial del usuario (default: True)
        is_staff: Privilegios de staff (default: False)
    """

    class Meta:
        model = User
        fields = ["username", "is_active", "is_staff"]

    def create(self, validated_data: dict[str, str]) -> get_user_model:
        """
        Crea usuario validando existencia en LDAP y extrayendo datos personales.

        Verifica que el usuario exista en el directorio LDAP corporativo,
        extrae información personal (email, nombres) y crea la cuenta con
        contraseña generada automáticamente.

        Args:
            validated_data: Datos validados del usuario a crear.
                          Debe incluir 'username' y opcionalmente 'is_active', 'is_staff'.

        Returns:
            User: Instancia del usuario creado con datos completos del LDAP.

        Raises:
            ValidationError: Si el usuario no existe en LDAP o hay error de conexión.
        """
        exist, _message = Ldap.connection_check_ldap(validated_data["username"])
        if exist:
            user = User.objects.create_user(
                username=validated_data["username"],
                email=_message["mail"],
                password=self.generate_password(),
                first_name=_message["givenName"],
                last_name=_message["sn"],
                is_active=validated_data.get("is_active", True),
                is_staff=validated_data.get("is_staff", False),
                is_superuser=False,
            )
            return user
        raise ValidationError({"username": "El usuario no existe en LDAP."})

    def generate_password(self, length: int = 12) -> str:
        """
        Genera contraseña aleatoria segura para el usuario.

        Crea una contraseña con caracteres alfanuméricos y especiales
        siguiendo estándares de seguridad para cuentas temporales.

        Args:
            length: Longitud de la contraseña (default: 12 caracteres).

        Returns:
            str: Contraseña aleatoria segura con mezcla de caracteres.
        """
        characters = string.ascii_letters + string.digits + string.punctuation
        return "".join(secrets.choice(characters) for _ in range(length))


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer para actualizar información básica de usuarios existentes.

    Permite modificar campos de configuración y estado del usuario,
    manteniendo la integridad de datos críticos como contraseñas
    y información personal extraída del LDAP.

    Fields:
        username: Nombre de usuario (solo lectura efectiva)
        is_active: Estado activo/inactivo del usuario
        is_staff: Privilegios de staff del usuario

    Note:
        No permite actualizar email, nombres o contraseña ya que estos
        datos se mantienen sincronizados con el sistema LDAP.
    """

    class Meta:
        model = User
        fields = ["username", "is_active", "is_staff"]


class InventorySerializer(serializers.ModelSerializer):
    """
    Serializer completo para gestión de inventario de dispositivos.

    Proporciona serialización completa de registros de inventario
    incluyendo campos calculados, relaciones anidadas y información
    de solo lectura para optimizar consultas.

    Features:
        - Campos calculados dinámicamente (íconos, nombres de relaciones)
        - Información de solo lectura para optimización de consultas
        - Serialización completa de especificaciones técnicas
        - Metadatos de auditoría y fechas críticas

    Computed Fields:
        type_device_icon: Ícono del tipo de dispositivo
        city_name: Nombre de la ciudad base
        base_headquarters_name: Nombre de la sede
        status_name: Nombre del estado del dispositivo
    """

    type_device_icon = serializers.SerializerMethodField()
    city_name = serializers.ReadOnlyField(source="base_city.name")
    base_headquarters_name = serializers.ReadOnlyField(source="base_headquarters.name")
    status_name = serializers.ReadOnlyField(source="status.name")

    def get_type_device_icon(self, obj: Inventory) -> str:
        """
        Obtiene el ícono asociado al tipo de dispositivo.

        Extrae el ícono configurado para el tipo de dispositivo
        del registro de inventario para uso en interfaces gráficas.

        Args:
            obj: Instancia del modelo Inventory.

        Returns:
            str: Código o ruta del ícono del tipo de dispositivo.
        """
        return obj.type_device.icon

    class Meta:
        model = Inventory
        fields = [
            "id",
            "base_city",
            "city_name",
            "base_headquarters",
            "base_headquarters_name",
            "location",
            "document",
            "company",
            "username",
            "type_device",
            "area",
            "floor",
            "manufacturer",
            "model",
            "serial",
            "license_plate",
            "ownership",
            "warehouse_entry_date",
            "network_user",
            "modality",
            "modality_tag",
            "comments",
            "status",
            "status_name",
            "machine_name",
            "warranty_date",
            "os",
            "processor_and_speed",
            "hard_drive",
            "memory",
            "total_memory_banks",
            "used_banks",
            "free_banks",
            "technology_renewal_date",
            "created_at",
            "updated_at",
            "type_device_icon",
            "delete_cause",
            "is_delete",
        ]


class TypeDeviceSerializer(serializers.ModelSerializer):
    """
    Serializer para tipos de dispositivos del inventario.

    Maneja la serialización completa de tipos de dispositivos incluyendo
    configuraciones específicas como años de vida útil, clase del dispositivo
    e íconos asociados.

    Features:
        - Información completa de configuración del tipo
        - Metadatos para cálculos automáticos de fechas
        - Clasificación de dispositivos (fijo/móvil)
    """

    class Meta(CommonMeta):
        model = TypeDevice


class BaseCitySerializer(serializers.ModelSerializer):
    """
    Serializer para ciudades base del sistema.

    Gestiona el catálogo de ciudades donde se ubican físicamente
    los dispositivos del inventario, incluyendo información de
    estado y metadatos de gestión.
    """

    class Meta(CommonMeta):
        model = BaseCity


class BaseHeadquartersSerializer(serializers.ModelSerializer):
    """
    Serializer para sedes o sucursales base.

    Administra información de sedes corporativas donde se encuentran
    ubicados los dispositivos, facilitando la gestión geográfica
    del inventario.
    """

    class Meta(CommonMeta):
        model = BaseHeadquarters


class CompanySerializer(serializers.ModelSerializer):
    """
    Serializer para compañías o empresas del sistema.

    Gestiona el catálogo de empresas asociadas a los dispositivos
    del inventario, permitiendo segmentación organizacional y
    reportes por entidad.
    """

    class Meta(CommonMeta):
        model = Company


class AreaSerializer(serializers.ModelSerializer):
    """
    Serializer para áreas organizacionales.

    Administra departamentos o áreas funcionales donde se asignan
    los dispositivos fijos, facilitando la organización interna
    y seguimiento de activos por dependencia.
    """

    class Meta(CommonMeta):
        model = Area


class ModalityTagSerializer(serializers.ModelSerializer):
    """
    Serializer para etiquetas de modalidad de dispositivos móviles.

    Gestiona clasificaciones especiales aplicables a dispositivos móviles,
    permitiendo categorización adicional según modalidad de uso
    """

    class Meta(CommonMeta):
        model = ModalityTag


class ManufacturerSerializer(serializers.ModelSerializer):
    """
    Serializer para fabricantes o marcas de dispositivos.

    Administra el catálogo de fabricantes de dispositivos tecnológicos,
    facilitando la gestión de garantías, soporte técnico y
    estadísticas por marca.
    """

    class Meta(CommonMeta):
        model = Manufacturer


class StatusDeviceSerializer(serializers.ModelSerializer):
    """
    Serializer para estados de dispositivos del inventario.

    Gestiona los diferentes estados que pueden tener los dispositivos
    durante su ciclo de vida.
    """

    class Meta(CommonMeta):
        model = StatusDevice
