"""
Configuración de la aplicación principal del sistema CMDB.

Este módulo contiene la configuración global de la aplicación Django
para el sistema de gestión de inventario CMDB (Configuration Management Database).
Define la configuración base, metadatos y comportamientos iniciales de la aplicación.
"""

from django.apps import AppConfig


class InventoryAppConfig(AppConfig):
    """
    Configuración principal de la aplicación del sistema CMDB.

    Define la configuración global y metadatos de la aplicación Django
    para el sistema de gestión de inventario CMDB, estableciendo
    comportamientos base y configuraciones de framework.
    """

    default_auto_field = "django.db.models.BigAutoField"
    name = "app"
