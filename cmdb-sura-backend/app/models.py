"""
Modelos de datos para el sistema de gestión de inventario CMDB.

Este módulo define todos los modelos de Django para la gestión completa
del inventario de activos tecnológicos, incluyendo entidades maestras
de configuración y el modelo principal de inventario con sus relaciones.

Architecture:
    Base Models:
        - CommonModel: Modelo abstracto base con funcionalidad común

    Master Data Models:
        - TypeDevice: Tipos de dispositivos con configuración de renovación
        - BaseCity: Catálogo de ciudades donde se ubican dispositivos
        - BaseHeadquarters: Sedes y sucursales organizacionales
        - Company: Empresas y organizaciones asociadas
        - Area: Departamentos y áreas funcionales
        - ModalityTag: Etiquetas de clasificación para dispositivos móviles
        - Manufacturer: Fabricantes y marcas de dispositivos
        - StatusDevice: Estados del ciclo de vida de dispositivos

    Main Models:
        - Inventory: Registro principal de dispositivos con especificaciones completas

Features:
    - Eliminación lógica en todos los modelos mediante CommonModel
    - Validación de unicidad con constraints personalizados
    - Relaciones ForeignKey optimizadas con related_names descriptivos
    - Campos opcionales y obligatorios según lógica de negocio
    - Soporte para dispositivos fijos y móviles con campos condicionales
    - Metadatos de auditoría con timestamps automáticos

Data Integrity:
    - Constraints de unicidad condicional para campos opcionales
    - Validación de relaciones obligatorias y opcionales
    - Eliminación lógica para preservar integridad referencial
    - Campos únicos para identificadores críticos (serial, license_plate)

Business Logic:
    - Diferenciación entre dispositivos fijos (requieren área/piso) y móviles
    - Cálculo automático de fechas de renovación basado en tipo de dispositivo
    - Clasificación por modalidad con etiquetas específicas
    - Gestión completa del ciclo de vida con estados configurables

Database Design:
    - Normalización completa con entidades maestras separadas
    - Indices implícitos en ForeignKey para optimización de consultas
    - Campos de texto con longitudes apropiadas para cada contexto
    - Relaciones CASCADE para mantener consistencia

Usage:
    Los modelos se utilizan para:
    - Gestión de inventario de dispositivos tecnológicos
    - Configuración de catálogos maestros
    - Reportes y estadísticas de activos
    - Importación/exportación masiva de datos
    - APIs REST para aplicaciones frontend

    Example:
        # Crear dispositivo en inventario
        device = Inventory.objects.create(
            serial='ABC123',
            type_device=TypeDevice.objects.get(name='Laptop'),
            base_city=BaseCity.objects.get(name='Cali'),
            # ... otros campos
        )

        # Consultar dispositivos por ubicación
        devices = Inventory.objects.filter(
            base_city__name='Cali',
            status__name='Activo'
        )

Dependencies:
    - django.db.models: Framework ORM de Django
    - app.commons.enums: Enumeraciones de campos del sistema
    - app.commons.consts: Constantes y opciones de configuración
"""

from django.db import models
from app.commons.enums import ModelFields, InventoryFields
from app.commons.consts import DEVICE_CLASS_CHOISES


class CommonModel(models.Model):
    """
    Modelo abstracto base para funcionalidad común de entidades maestras.

    Proporciona funcionalidad estándar de eliminación lógica para todos
    los modelos del sistema, permitiendo mantener integridad referencial
    sin eliminar físicamente registros de la base de datos.

    Features:
        - Eliminación lógica mediante campo is_delete
        - Base para herencia en todos los modelos maestros
        - Funcionalidad común reutilizable

    Fields:
        is_delete: Marca de eliminación lógica (default: False)

    Usage:
        Se utiliza como clase base para todos los modelos que requieren
        eliminación lógica y funcionalidad común del sistema.
    """

    is_delete = models.BooleanField(ModelFields.IS_DELETED.value, default=False)

    class Meta:
        abstract = True


class TypeDevice(CommonModel):
    """
    Modelo para tipos de dispositivos tecnológicos del inventario.

    Define las categorías de dispositivos disponibles en el sistema
    con configuración específica para cálculos automáticos de fechas
    de renovación tecnológica y clasificación por modalidad.

    Features:
        - Configuración de años de renovación por tipo
        - Clasificación automática entre dispositivos fijos y móviles
        - Soporte para íconos personalizados en interfaces
        - Estado activo/inactivo para gestión del catálogo

    Fields:
        name: Nombre único del tipo de dispositivo
        years: Años de vida útil para cálculo de renovación
        status: Estado activo/inactivo del tipo
        device_class: Clasificación fijo/móvil para lógica condicional
        icon: Código de ícono para interfaces gráficas

    Business Rules:
        - Cada tipo tiene años específicos de renovación tecnológica
        - La clase determina campos obligatorios en inventario
        - Solo tipos activos aparecen en formularios de carga
    """

    name = models.CharField(ModelFields.NAME.value, max_length=100, unique=True)
    years = models.PositiveIntegerField("Años de renovación")
    status = models.BooleanField(ModelFields.STATE.value, default=True)
    device_class = models.CharField(
        "Clase del dispositivo", choices=DEVICE_CLASS_CHOISES, max_length=5
    )
    icon = models.CharField("Icono", max_length=100, blank=True)

    class Meta:
        verbose_name = InventoryFields.TYPE_DEVICE
        verbose_name_plural = "Tipos de dispositivo"

    def __str__(self) -> str:
        return self.name


class BaseCity(CommonModel):
    """
    Modelo para ciudades base donde se ubican los dispositivos.

    Catálogo de ciudades corporativas que permite la clasificación
    geográfica de dispositivos para reportes y gestión logística.

    Features:
        - Catálogo centralizado de ciudades
        - Estado activo/inactivo para gestión del catálogo
        - Relación con sedes para organización jerárquica

    Fields:
        name: Nombre único de la ciudad
        status: Estado activo/inactivo de la ciudad

    Usage:
        Se utiliza para organizar geográficamente el inventario
        y generar reportes por ubicación física.
    """

    name = models.CharField(ModelFields.NAME.value, max_length=100, unique=True)
    status = models.BooleanField(ModelFields.STATE.value, default=True)

    class Meta:
        verbose_name = InventoryFields.BASE_CITY
        verbose_name_plural = "Ciudades base"

    def __str__(self) -> str:
        return self.name


class BaseHeadquarters(CommonModel):
    """
    Modelo para sedes y sucursales organizacionales.

    Define las instalaciones físicas específicas donde se encuentran
    los dispositivos, permitiendo ubicación precisa dentro de ciudades
    para gestión detallada de activos.

    Features:
        - Ubicaciones específicas dentro de ciudades
        - Gestión de múltiples sedes por organización
        - Estado activo/inactivo para control del catálogo

    Fields:
        name: Nombre único de la sede o sucursal
        status: Estado activo/inactivo de la sede

    Business Rules:
        - Cada sede pertenece implícitamente a una ciudad
        - Solo sedes activas aparecen en formularios
        - Requerido para todos los dispositivos del inventario
    """

    name = models.CharField(ModelFields.NAME.value, max_length=100, unique=True)
    status = models.BooleanField(ModelFields.STATE.value, default=True)

    class Meta:
        verbose_name = InventoryFields.BASE_HEADQUARTERS
        verbose_name_plural = "Sedes base"

    def __str__(self) -> str:
        return self.name


class Company(CommonModel):
    """
    Modelo para empresas y organizaciones asociadas al inventario.

    Catálogo de entidades organizacionales que poseen o gestionan
    dispositivos, permitiendo segmentación y reportes por empresa.

    Features:
        - Segmentación organizacional de dispositivos
        - Soporte para múltiples empresas en sistema corporativo
        - Estado activo/inactivo para gestión del catálogo

    Fields:
        name: Nombre único de la empresa u organización
        status: Estado activo/inactivo de la empresa

    Usage:
        Facilita reportes por entidad organizacional y gestión
        de activos en estructuras empresariales complejas.
    """

    name = models.CharField(ModelFields.NAME.value, max_length=100, unique=True)
    status = models.BooleanField(ModelFields.STATE.value, default=True)

    class Meta:
        verbose_name = InventoryFields.COMPANY

    def __str__(self) -> str:
        return self.name


class Area(CommonModel):
    """
    Modelo para áreas y departamentos organizacionales.

    Define departamentos funcionales donde se asignan dispositivos
    fijos, facilitando la organización interna y seguimiento de
    activos por dependencia.

    Features:
        - Organización funcional de dispositivos fijos
        - Campo obligatorio solo para dispositivos de clase 'fijo'
        - Estado activo/inactivo para gestión del catálogo

    Fields:
        name: Nombre único del área o departamento
        status: Estado activo/inactivo del área

    Business Rules:
        - Requerido únicamente para dispositivos fijos
        - Dispositivos móviles no requieren asignación de área
        - Solo áreas activas aparecen en formularios
    """

    name = models.CharField(ModelFields.NAME.value, max_length=100, unique=True)
    status = models.BooleanField(ModelFields.STATE.value, default=True)

    class Meta:
        verbose_name = InventoryFields.AREA

    def __str__(self) -> str:
        return self.name


class ModalityTag(CommonModel):
    """
    Modelo para etiquetas de modalidad de dispositivos móviles.

    Proporciona clasificación adicional específica para dispositivos
    móviles según su modalidad de uso.

    Features:
        - Clasificación específica para dispositivos móviles
        - Campo opcional para mayor flexibilidad
        - Estado activo/inactivo para gestión del catálogo

    Fields:
        name: Nombre único de la etiqueta de modalidad
        status: Estado activo/inactivo de la etiqueta

    Business Rules:
        - Aplicable únicamente a dispositivos de clase 'móvil'
        - Campo opcional incluso para dispositivos móviles
        - Dispositivos fijos no utilizan etiquetas de modalidad
    """

    name = models.CharField(ModelFields.NAME.value, max_length=100, unique=True)
    status = models.BooleanField(ModelFields.STATE.value, default=True)

    class Meta:
        verbose_name = InventoryFields.MODALITY_TAG
        verbose_name_plural = "Etiquetas de modalidad"

    def __str__(self) -> str:
        return self.name


class Manufacturer(CommonModel):
    """
    Modelo para fabricantes y marcas de dispositivos tecnológicos.

    Catálogo de fabricantes que permite gestión de garantías,
    soporte técnico y estadísticas de dispositivos por marca.

    Features:
        - Catálogo centralizado de fabricantes
        - Soporte para gestión de garantías por marca
        - Estado activo/inactivo para control del catálogo

    Fields:
        name: Nombre único del fabricante o marca
        status: Estado activo/inactivo del fabricante

    Usage:
        Facilita reportes por fabricante, gestión de garantías
        y análisis de distribución de marcas en el inventario.
    """

    name = models.CharField(ModelFields.NAME.value, max_length=100, unique=True)
    status = models.BooleanField(ModelFields.STATE.value, default=True)

    class Meta:
        verbose_name = InventoryFields.MANUFACTURER

    def __str__(self) -> str:
        return self.name


class StatusDevice(CommonModel):
    """
    Modelo para estados del ciclo de vida de dispositivos.

    Define los diferentes estados que pueden tener los dispositivos
    durante su ciclo de vida operacional (activo, inactivo, en
    reparación, dado de baja, etc.).

    Features:
        - Gestión completa del ciclo de vida de dispositivos
        - Estados configurables según necesidades organizacionales
        - Estado activo/inactivo para gestión del catálogo

    Fields:
        name: Nombre único del estado del dispositivo
        status: Estado activo/inactivo del estado (meta-estado)

    Business Rules:
        - Requerido para todos los dispositivos del inventario
        - Estados inactivos no aparecen en formularios nuevos
        - Dispositivos existentes mantienen estados inactivados
    """

    name = models.CharField(ModelFields.NAME.value, max_length=100, unique=True)
    status = models.BooleanField(ModelFields.STATE.value, default=True)

    class Meta:
        verbose_name = InventoryFields.STATUS
        verbose_name_plural = "Estados de dispositivo"

    def __str__(self) -> str:
        return self.name


class Inventory(CommonModel):
    """
    Modelo principal para registros de inventario de dispositivos.

    Registro completo de dispositivos tecnológicos con información
    de ubicación, especificaciones técnicas, fechas críticas y
    metadatos de gestión. Modelo central del sistema CMDB.

    Features:
        - Información completa de dispositivos (ubicación, especificaciones, fechas)
        - Campos condicionales según tipo de dispositivo (fijo/móvil)
        - Validación de unicidad para identificadores críticos
        - Relaciones optimizadas con entidades maestras
        - Metadatos de auditoría automáticos
        - Constraints personalizados para integridad de datos

    Location Fields:
        base_city: Ciudad donde se ubica el dispositivo (requerido)
        base_headquarters: Sede específica del dispositivo (requerido)
        location: Ubicación específica dentro de la sede (opcional)
        area: Área funcional (requerido solo para dispositivos fijos)
        floor: Piso de ubicación (requerido solo para dispositivos fijos)

    Assignment Fields:
        document: Documento de identificación del usuario asignado
        company: Empresa propietaria del dispositivo
        username: Usuario asignado al dispositivo
        network_user: Usuario de red (opcional)

    Device Fields:
        type_device: Tipo de dispositivo con configuración de renovación
        manufacturer: Fabricante del dispositivo
        model: Modelo específico del dispositivo
        serial: Número de serie único del dispositivo
        license_plate: Placa de identificación interna (opcional, único)
        machine_name: Nombre de máquina en red (opcional, único)
        modality: Modalidad calculada automáticamente (fijo/móvil)
        modality_tag: Etiqueta adicional (solo para dispositivos móviles)
        ownership: Tipo de propiedad del dispositivo
        status: Estado actual del dispositivo

    Technical Fields:
        os: Sistema operativo instalado (opcional)
        processor_and_speed: Especificaciones del procesador (opcional)
        hard_drive: Información de capacidad del disco duro (opcional)
        memory: Memoria RAM instalada (opcional)
        total_memory_banks: Total de bancos de memoria (opcional)
        used_banks: Bancos de memoria utilizados (opcional)
        free_banks: Bancos de memoria disponibles (opcional)

    Date Fields:
        warehouse_entry_date: Fecha de ingreso al almacén (requerido)
        warranty_date: Fecha de vencimiento de garantía (requerido)
        technology_renewal_date: Fecha calculada de renovación (requerido)
        created_at: Timestamp de creación del registro (automático)

    Other Fields:
        comments: Comentarios adicionales (opcional)

    Constraints:
        - Serial único obligatorio para todos los dispositivos
        - License_plate único cuando no es nulo o vacío
        - Machine_name único cuando no es nulo o vacío

    Business Rules:
        - Dispositivos fijos requieren área y piso
        - Dispositivos móviles pueden usar etiquetas de modalidad
        - Modalidad se calcula automáticamente desde type_device
        - Fecha de renovación se calcula desde fecha de ingreso + años del tipo
        - Campos técnicos son opcionales para flexibilidad

    Performance Considerations:
        - Indices automáticos en todas las ForeignKey
        - Related_names descriptivos para consultas inversas
        - Constraints de base de datos para validación eficiente
    """

    base_city = models.ForeignKey(
        BaseCity,
        verbose_name=InventoryFields.BASE_CITY,
        related_name="base_cities",
        on_delete=models.CASCADE,
    )
    base_headquarters = models.ForeignKey(
        BaseHeadquarters,
        verbose_name=InventoryFields.BASE_HEADQUARTERS,
        related_name="base_headquarters",
        on_delete=models.CASCADE,
    )
    location = models.CharField(InventoryFields.LOCATION, max_length=100, blank=True)
    document = models.CharField(InventoryFields.DOCUMENT, max_length=100)
    company = models.ForeignKey(
        Company,
        verbose_name=InventoryFields.COMPANY,
        related_name="companies",
        on_delete=models.CASCADE,
    )
    username = models.CharField(InventoryFields.USERNAME, max_length=100)
    type_device = models.ForeignKey(
        TypeDevice,
        verbose_name=InventoryFields.TYPE_DEVICE,
        related_name="type_devices",
        on_delete=models.CASCADE,
    )
    area = models.ForeignKey(
        Area,
        verbose_name=InventoryFields.AREA,
        on_delete=models.CASCADE,
        related_name="areas",
        null=True,
        blank=True,
    )
    floor = models.CharField(InventoryFields.FLOOR, max_length=100, blank=True)
    manufacturer = models.ForeignKey(
        Manufacturer,
        verbose_name=InventoryFields.MANUFACTURER,
        related_name="manufacturers",
        on_delete=models.CASCADE,
    )
    model = models.CharField(InventoryFields.MODEL, max_length=100)
    serial = models.CharField(InventoryFields.SERIAL, unique=True, max_length=100)
    license_plate = models.CharField(
        InventoryFields.LICENSE_PLATE, max_length=100, blank=True
    )
    ownership = models.CharField(InventoryFields.OWNERSHIP, max_length=100)
    warehouse_entry_date = models.DateField(InventoryFields.WAREHOUSE_ENTRY_DATE)
    network_user = models.CharField(
        InventoryFields.NETWORK_USER, max_length=100, blank=True
    )
    modality = models.CharField(InventoryFields.MODALITY, max_length=100)
    modality_tag = models.ForeignKey(
        ModalityTag,
        verbose_name=InventoryFields.MODALITY_TAG,
        related_name="modality_tags",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    comments = models.CharField(InventoryFields.COMMENTS, max_length=100, blank=True)
    status = models.ForeignKey(
        StatusDevice,
        verbose_name=InventoryFields.STATUS,
        related_name="status_devices",
        on_delete=models.CASCADE,
    )
    machine_name = models.CharField(
        InventoryFields.MACHINE_NAME, max_length=100, blank=True
    )
    warranty_date = models.DateField(InventoryFields.WARRANTY_DATE)
    os = models.CharField(InventoryFields.OS, max_length=100, blank=True)
    processor_and_speed = models.CharField(
        InventoryFields.PROCESSOR_AND_SPEED, max_length=100, blank=True
    )
    hard_drive = models.CharField(
        InventoryFields.HARD_DRIVE, max_length=100, blank=True
    )
    memory = models.CharField(InventoryFields.MEMORY, max_length=100, blank=True)
    total_memory_banks = models.CharField(
        InventoryFields.TOTAL_MEMORY_BANKS, max_length=100, blank=True
    )
    used_banks = models.CharField(
        InventoryFields.USED_BANKS, max_length=100, blank=True
    )
    free_banks = models.CharField(
        InventoryFields.FREE_BANKS, max_length=100, blank=True
    )
    technology_renewal_date = models.DateField(InventoryFields.TECHNOLOGY_RENEWAL_DATE)
    created_at = models.DateTimeField("Fecha creación", auto_now_add=True)
    updated_at = models.DateTimeField("Fecha actualización", auto_now=True)
    delete_cause = models.CharField(
        InventoryFields.DELETE_CAUSE, max_length=200, blank=True
    )

    class Meta:
        verbose_name = "Inventario"
        constraints = [
            models.UniqueConstraint(
                fields=["license_plate"],
                name="unique_license_plate",
                condition=~models.Q(license_plate__isnull=True)
                & ~models.Q(license_plate=""),
            ),
            models.UniqueConstraint(
                fields=["machine_name"],
                name="unique_machine_name",
                condition=~models.Q(machine_name__isnull=True)
                & ~models.Q(machine_name=""),
            ),
        ]

    def __str__(self) -> str:
        return self.machine_name
