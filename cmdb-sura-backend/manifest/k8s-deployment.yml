# ---
# apiVersion: v1
# kind: PersistentVolume
# metadata:
#   name: images-parameters.namespace-volume
# spec:
#   storageClassName: manual
#   capacity:
#     storage: 10Gi
#   accessModes:
#     - ReadWriteMany
#   hostPath:
#     path: "/opt/k3s_volumes/cmdb_media"
#   persistentVolumeReclaimPolicy: Retain
# ---
# apiVersion: v1
# kind: PersistentVolumeClaim
# metadata:
#   name: images-parameters.namespace-volume
#   namespace: parameters.namespace
# spec:
#   accessModes:
#     - ReadWriteMany
#   storageClassName: manual
#   resources:
#     requests:
#       storage: 10Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: parameters.imageRepository
  namespace: parameters.namespace
spec:
  selector:
    matchLabels:
      app: parameters.imageRepository
  template:
    metadata:
      labels:
        app: parameters.imageRepository
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      nodeSelector:
        app6: cmdb
      containers:
        - name: parameters.imageRepository
          image: parameters.containerRegistry/parameters.harborProject/parameters.imageRepository:parameters.tag
          imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: cmdb-secrets
          env:
            - name: DB_NAME
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: POSTGRES_DB
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: POSTGRES_USER
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: POSTGRES_PASSWORD
            - name: KEYCLOAK_TIMEOUT
              value: "30"
            - name: GNICORN_TIMEOUT
              value: "120"
            - name: ALLOWED_HOSTS
              value: parameters.allowedHosts
            - name: CSRF_TRUSTED_ORIGINS
              value: https://parameters.dns
          resources:
            requests:
              memory: "1Gi"
              cpu: "300m"
              ephemeral-storage: "500Mi"
            limits:
              memory: "2Gi"
              cpu: "500m"
              ephemeral-storage: "700Mi"
          ports:
            - containerPort: 8000
          volumeMounts:
            - name: parameters.imageRepository-vol
              mountPath: /app/media
      volumes:
        - name: parameters.imageRepository-vol
          persistentVolumeClaim:
            claimName: images-parameters.namespace-volume
      imagePullSecrets:
        - name: regcred
      automountServiceAccountToken: false
---
apiVersion: v1
kind: Service
metadata:
  name: parameters.imageRepository
  namespace: parameters.namespace
spec:
  selector:
    app: parameters.imageRepository
  type: ClusterIP
  ports:
    - name: parameters.imageRepository
      protocol: TCP
      port: 8000
      targetPort: 8000
