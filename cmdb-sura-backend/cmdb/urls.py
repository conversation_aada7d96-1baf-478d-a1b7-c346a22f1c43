"""
Configuración de URLs para el sistema de gestión de inventario CMDB.

Este módulo define todas las rutas y endpoints del sistema, organizando
la estructura de URLs para APIs REST, autenticación, administración y
funcionalidades especializadas de importación/exportación de datos.

URL Structure:
    API Endpoints:
        /api/ - Router principal con ViewSets registrados
        /api/token/ - Endpoints de autenticación JWT
        /api/export/ - Exportación de inventario a Excel
        /api/generate/ - Generación de plantillas Excel
        /api/import/ - Importación masiva desde Excel
    Administrative:
        /admin/ - Interfaz de administración Django
"""

from django.contrib import admin
from django.conf.urls.static import static
from django.conf import settings
from django.urls import include, path
from rest_framework import routers
from rest_framework_simplejwt.views import TokenRefreshView
from app.views.inventory_views import InventoryViewSet
from app.views.managed_fields_views import (
    TypeDeviceViewSet,
    BaseCityViewSet,
    BaseHeadquartersViewSet,
)
from app.views.managed_fields_views import (
    CompanyViewSet,
    AreaViewSet,
)
from app.views.managed_fields_views import (
    ModalityTagViewSet,
    ManufacturerViewSet,
    StatusDeviceViewSet,
)
from app.views.inventory_views import (
    ExportarExcelAPIView,
    ExcelImportAPIView,
    GenerateExcelTemplateView,
)
from app.views.user_views import UserViewSet
from app.views.auth_views import TokenCodeObtainPairView


router = routers.DefaultRouter()
router.register(r"users", UserViewSet)
router.register(r"inventory", InventoryViewSet)
router.register(r"type_device", TypeDeviceViewSet)
router.register(r"base_city", BaseCityViewSet)
router.register(r"base_headquarters", BaseHeadquartersViewSet)
router.register(r"company", CompanyViewSet)
router.register(r"area", AreaViewSet)
router.register(r"modality_tag", ModalityTagViewSet)
router.register(r"manufacturer", ManufacturerViewSet)
router.register(r"status_device", StatusDeviceViewSet)
urlpatterns = [
    path("api/", include(router.urls)),
    path("admin/", admin.site.urls),
    path(
        "api/token/code",
        TokenCodeObtainPairView.as_view(),
        name="token_obtain_pair_code",
    ),
    path("api/token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("api/export/", ExportarExcelAPIView.as_view(), name="export"),
    path(
        "api/generate/",
        GenerateExcelTemplateView.as_view(),
        name="generar_plantilla_excel",
    ),
    path("api/import/", ExcelImportAPIView.as_view(), name="importar_inventario_excel"),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
