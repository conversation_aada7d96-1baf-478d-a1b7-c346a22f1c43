"""
Serializadores personalizados de autenticación para el sistema CMDB.

Este módulo contiene serializadores especializados para la autenticación
de usuarios utilizando integración con Keycloak como proveedor de identidad,
proporcionando autenticación segura con tokens JWT y flujos OAuth2.

Components:
    Exception Classes:
        - CustomAuthenticationFailed: Excepción personalizada para errores de autenticación

    Serializer Classes:
        - CustomObtainPairSerializer: Autenticación tradicional con username/password
        - CustomCodeObtainPairSerializer: Autenticación OAuth2 con código de autorización

Authentication Flows:
    Username/Password Flow:
        1. Usuario proporciona credenciales (username/password)
        2. Validación contra Keycloak
        3. Verificación de usuario en base de datos local
        4. Generación de tokens JWT para la aplicación

    OAuth2 Authorization Code Flow:
        1. Usuario obtiene código de autorización desde Keycloak
        2. Intercambio de código por tokens de acceso
        3. Validación de tokens con Keycloak
        4. Sincronización con usuario local
        5. Generación de tokens JWT para la aplicación

Dependencies:
    - django.contrib.auth: Sistema de usuarios de Django
    - rest_framework_simplejwt: Tokens JWT para DRF
    - app.lib.keycloak_config: Cliente personalizado de Keycloak
    - rest_framework: Framework base para serialización
"""

import logging
from django.contrib.auth import get_user_model
from django.core.exceptions import ImproperlyConfigured
from rest_framework_simplejwt import tokens, serializers as jwt_serializers
from rest_framework import serializers, exceptions
from app.lib.keycloak_config import KeycloakAuth, KeycloakAuthenticationFailed
from app.models import Inventory


logging.basicConfig(format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("jwtSerializer")

keycloak_auth = KeycloakAuth()


class CustomAuthenticationFailed(exceptions.AuthenticationFailed):
    """
    Excepción personalizada para errores de autenticación del sistema.

    Extiende la excepción base AuthenticationFailed de DRF para proporcionar
    mensajes de error personalizados en español y códigos de estado HTTP
    específicos para diferentes escenarios de fallo de autenticación.

    Attributes:
        status_code: Código HTTP 400 (Bad Request) para errores de autenticación
        default_detail: Mensaje genérico para credenciales inválidas
        default_code: Código interno para clasificación de errores
    """

    status_code = 400
    default_detail = "El usuario o la contraseña suministrada no son validos."
    default_code = "bad_request"


class CustomObtainPairSerializer(jwt_serializers.TokenObtainPairSerializer):
    """
    Serializador personalizado para autenticación con username y password.

    Extiende TokenObtainPairSerializer de DRF-SimpleJWT para integrar
    autenticación con Keycloak manteniendo compatibilidad con el sistema
    local de usuarios de Django. Implementa validación dual: Keycloak
    para credenciales y Django para permisos locales.

    Authentication Flow:
        1. Validación de existencia del usuario en base de datos local
        2. Verificación de credenciales contra Keycloak
        3. Inspección y validación del token recibido
        4. Generación de tokens JWT locales
        5. Construcción de respuesta con información del usuario
    """

    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.user = None

    def create(self, validated_data: dict[str, str]) -> None:
        """Este método no se utiliza pero es requisito del BaseSerializer"""

    def update(self, instance: Inventory, validated_data: dict[str, str]) -> None:
        """Este método no se utiliza pero es requisito del BaseSerializer"""

    def _get_user(self, attrs: dict[str, str]) -> None:
        try:
            self.user = get_user_model().objects.get(username=attrs["username"])
        except ImproperlyConfigured as e:
            logger.error("Error al obtener el usuario: %s", str(e))
            raise CustomAuthenticationFailed(
                "El usuario no se encuentra registrado."
            ) from e
        if not self.user.is_active:
            logger.warning("Usuario inactivo: %s", self.user.username)
            raise CustomAuthenticationFailed("La cuenta ha sido bloqueada.")

    def _validate_ldap_user(self, attrs: dict[str, str]) -> tuple[str, dict[str, str]]:
        try:
            keycloak_token = keycloak_auth.get_user_token(attrs)
            data_return = keycloak_auth.inspect_token(keycloak_token["token"])
            return tokens.RefreshToken.for_user(self.user), data_return
        except KeycloakAuthenticationFailed as e:
            logger.error("Error de autenticación con Keycloak: %s", str(e))
            raise CustomAuthenticationFailed(str(e)) from e

    def validate(self, attrs: dict[str, str]) -> dict[str, str]:
        self._get_user(attrs)
        data, info_user = self._validate_ldap_user(attrs)
        return {
            "id": self.user.id,
            "access_token": str(data.access_token),
            "refresh_token": str(data),
            "name": info_user["name"],
            "email": info_user["email"],
            "given_name": info_user["given_name"],
            "family_name": info_user["family_name"],
            "username": info_user["username"],
            "is_staff": self.user.is_staff,
            "is_superuser": self.user.is_superuser,
            "active": info_user["active"],
        }


class CustomCodeObtainPairSerializer(serializers.Serializer):
    """
    Serializador para autenticación OAuth2 mediante código de autorización.

    Implementa el flujo OAuth2 Authorization Code para autenticación segura
    sin manejo directo de credenciales en la aplicación. El usuario se autentica
    en Keycloak directamente y la aplicación recibe un código que intercambia
    por tokens de acceso.

    OAuth2 Flow:
        1. Usuario es redirigido a Keycloak para autenticación
        2. Keycloak autentica al usuario y genera código de autorización
        3. Usuario es redirigido de vuelta con el código
        4. Aplicación intercambia código por tokens de acceso
        5. Tokens son validados e información del usuario es extraída
        6. Usuario local es sincronizado y tokens JWT locales son generados

    Input Fields:
        code: Código de autorización recibido desde Keycloak
        redirect_url: URL de redirección utilizada en el flujo OAuth2

    Output Data:
        Similar a CustomObtainPairSerializer pero incluye:
        - id_token: Token de identidad adicional de Keycloak
        - Toda la información estándar del usuario y permisos
    """

    code = serializers.CharField(label="Código", required=True)
    redirect_url = serializers.CharField(label="Url de redirección", required=True)

    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.user = None

    def create(self, validated_data: dict[str, str]) -> None:
        """Este método no se utiliza pero es requisito del BaseSerializer"""

    def update(self, instance: Inventory, validated_data: dict[str, str]) -> None:
        """Este método no se utiliza pero es requisito del BaseSerializer"""

    def _get_user(self, username: str) -> None:
        try:
            self.user = get_user_model().objects.get(username=username)
        except ImproperlyConfigured as e:
            logger.error("Error al obtener el usuario: %s", str(e))
            raise CustomAuthenticationFailed(
                "El usuario no se encuentra registrado."
            ) from e
        if not self.user.is_active:
            logger.warning("Usuario inactivo: %s", self.user.username)
            raise CustomAuthenticationFailed("El usuario se encuentra inactivo.")

    def validate(self, attrs: dict[str, str]) -> dict[str, str]:
        try:
            keycloak_token = keycloak_auth.get_user_token_with_code(
                attrs["code"], attrs["redirect_url"]
            )
            info_user = keycloak_auth.inspect_token(keycloak_token["token"])
            self._get_user(info_user["preferred_username"])
            data = tokens.RefreshToken.for_user(self.user)
            return {
                "id": self.user.id,
                "access_token": str(data.access_token),
                "refresh_token": str(data),
                "name": info_user["name"],
                "email": info_user["email"],
                "given_name": info_user["given_name"],
                "family_name": info_user["family_name"],
                "username": info_user["username"],
                "is_staff": self.user.is_staff,
                "is_superuser": self.user.is_superuser,
                "active": info_user["active"],
                "id_token": keycloak_token["id_token"],
            }
        except KeycloakAuthenticationFailed as e:
            logger.error("Error de autenticación en Keycloak: %s", str(e))
            raise CustomAuthenticationFailed("Error de autenticación en Keycloak.") from e
