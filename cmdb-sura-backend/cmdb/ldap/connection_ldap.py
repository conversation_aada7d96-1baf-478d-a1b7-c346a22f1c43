"""
Cliente LDAP para autenticación e integración con Active Directory corporativo.

Este módulo proporciona funcionalidades de conexión, autenticación y consulta
de usuarios contra un servidor LDAP/Active Directory, facilitando la integración
del sistema CMDB con la infraestructura de identidad corporativa existente.

Components:
    - Ldap: Clase principal con métodos de conexión y autenticación LDAP

LDAP Configuration:
    Requiere configuración en settings.py:
    - LDAP_SERVER: Dirección del servidor LDAP/AD
    - LDAP_USER: Usuario de servicio para consultas
    - LDAP_SECRET: Contraseña del usuario de servicio

Dependencies:
    - ldap3: Cliente LDAP moderno para Python
    - django.conf.settings: Configuración de conexión
    - logging: Auditoría y debugging de operaciones
"""

import logging
from django.conf import settings
from ldap3 import ALL, SUBTREE, Connection, Server
from ldap3.core.exceptions import LDAPException


logger = logging.getLogger("pyLdap")


class Ldap:
    """
    Cliente LDAP para operaciones de autenticación y consulta de usuarios.

    Proporciona métodos para interactuar con un servidor LDAP/Active Directory
    corporativo, incluyendo autenticación de usuarios, consulta de información
    de perfil y validación de existencia de cuentas de usuario.

    LDAP Connection:
        - Servidor: Configurado en settings.LDAP['LDAP_SERVER']
        - Credenciales de servicio: LDAP_USER y LDAP_SECRET
        - Base DN: DC=da,DC=arus,DC=com,DC=co (dominio corporativo)
        - Protocolo: LDAP v3 con soporte para búsquedas avanzadas

    User Attributes:
        Extrae atributos estándar de Active Directory:
        - displayName: Nombre completo para mostrar
        - mail: Correo electrónico corporativo
        - givenName: Nombre(s) de pila
        - sn: Apellido(s) del usuario
        - distinguishedName: DN completo para autenticación
        - postOfficeBox: Información organizacional adicional
    """

    def connection_ldap(
        self, username: str, password: str
    ) -> tuple[bool, str] | tuple[bool, dict[str, str]]:
        """Función para conectarse al ldap"""
        try:
            error_message = "No se encontró información del usuario indicado"
            server = Server(settings.LDAP["LDAP_SERVER"], get_info=ALL)
            conn = Connection(
                server, settings.LDAP["LDAP_USER"], settings.LDAP["LDAP_SECRET"]
            )
            search_filter = f"(SamAccountName={username})"
            conn.open()
            conn.bind()
            data_ldap_string = {}
            return_message = ()
            if conn.search(
                search_base="DC=da,DC=arus,DC=com,DC=co",
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=[
                    "displayName",
                    "postOfficeBox",
                    "mail",
                    "givenName",
                    "sn",
                    "distinguishedName",
                ],
            ):
                data_ldap = {}
                for entry in conn.response:
                    if entry["type"] == "searchResEntry":
                        data_ldap = entry["attributes"]
                if self.authenticate_ldap_user(
                    data_ldap["distinguishedName"], password
                ):
                    for data in data_ldap:
                        data_ldap_string[data] = "".join(data_ldap[data])
                    conn.unbind()
                    return_message = (True, data_ldap_string)
                else:
                    conn.unbind()
                    return_message = (False, "Usuario o contraseña incorrecto")
            else:
                conn.unbind()
                return_message = (False, error_message)
            return return_message
        except LDAPException as e:
            logger.error(str(e))
            return (
                False,
                f"Se presentaron problemas estableciendo la conexión con LDAP: {str(e)}",
            )

    @staticmethod
    def authenticate_ldap_user(user: str, password: str) -> bool:
        """Función para autenticar el usuario en el ldap"""
        server = Server(settings.LDAP["LDAP_SERVER"], get_info=ALL)
        conn = Connection(server, user, password)
        conn.open()
        if conn.bind():
            return True
        conn.unbind()
        return False

    @staticmethod
    def connection_check_ldap(
        username: str,
    ) -> tuple[bool, str] | tuple[bool, dict[str, str]]:
        """función para conectarse al ldap"""
        try:
            error_message = "No se encontró información del usuario indicado"
            server = Server(settings.LDAP["LDAP_SERVER"], get_info=ALL)
            conn = Connection(
                server, settings.LDAP["LDAP_USER"], settings.LDAP["LDAP_SECRET"]
            )
            search_filter = f"(SamAccountName={username})"
            conn.open()
            conn.bind()
            data_ldap = None
            return_message = (False, "")
            if conn.search(
                search_base="DC=da,DC=arus,DC=com,DC=co",
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=[
                    "displayName",
                    "postOfficeBox",
                    "mail",
                    "givenName",
                    "sn",
                    "distinguishedName",
                ],
            ):
                for entry in conn.response:
                    if entry["type"] == "searchResEntry":
                        data_ldap = entry["attributes"]
                conn.unbind()
                if data_ldap:
                    return_message = (True, data_ldap)
                else:
                    logger.error(conn.response)
                    return_message = (False, error_message)
            else:
                conn.unbind()
                return_message = (False, error_message)
            return return_message
        except LDAPException as e:
            logger.error(str(e))
            return False, f"Se presentaron problemas estableciendo la conexión con LDAP: {str(e)}"
