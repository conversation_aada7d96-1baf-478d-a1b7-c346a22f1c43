FROM python:3.12
WORKDIR /app
RUN apt-get update && apt-get install -y --no-install-recommends
COPY ./app /app/app
COPY ./cmdb /app/cmdb
COPY ./requirements.txt /app/requirements.txt
COPY ./manage.py /app/manage.py
COPY ./startup.sh /app/startup.sh
RUN pip install -r requirements.txt && \ 
    mkdir -p /app/media /app/static /app/app/migrations && touch /app/app/migrations/__init__.py
EXPOSE 8000
RUN useradd -ms /bin/bash app && \
    chown -R app:app /app && \
    chmod +x /app/startup.sh
USER app
CMD ["/app/startup.sh"]
