---
resources:
  repositories:
    - repository: templates
      type: git
      name: pipeline-transversal
      ref: refs/heads/main

trigger:
  branches:
    include:
      - main

pr:
  branches:
    include:
      - develop

variables:
  - template: variables-main.yml
  - group: cmdb-sura-variables-group-prod
  - group: sonarqube-variables-group

extends:
  template: pipeline-template.yml@templates
  parameters:
    imageRepository: ${{ variables.imageRepository }}
    dockerfilePath: ${{ variables.dockerfilePath }}
    tag: ${{ variables.tag }}
    technology: ${{ variables.technology }}
    nodeVersion: ${{ variables.nodeVersion }}
    projectKey: ${{ variables.projectKey }}
    agentPoolDeploy: ${{ variables.agentPoolDeploy }}
    hostnameDeploy: ${{ variables.hostnameDeploy }}
