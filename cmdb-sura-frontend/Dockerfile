FROM node:lts-alpine AS build-stage
WORKDIR /app

RUN npm install -g --ignore-scripts @quasar/cli

COPY .eslintrc.cjs ./
COPY .eslintignore ./ 
COPY tsconfig.json ./
COPY tsconfig.vue-tsc.json ./
COPY tsconfig-preset.json ./
COPY postcss.config.cjs ./
COPY quasar.config.js ./
COPY package.json ./
COPY .env.prod .env

RUN npm install --ignore-scripts

COPY src ./src
COPY public ./public
COPY index.html ./ 

RUN quasar build

FROM nginx:stable-alpine AS production-stage

RUN addgroup -S nodegroup && adduser -S node -G nodegroup \
    && chown -R node:nodegroup /usr/share/nginx/html /etc/nginx /var/cache/nginx /run/

USER node

COPY ./nginx-default.conf /etc/nginx/conf.d/default.conf

COPY --from=build-stage /app/dist/spa /usr/share/nginx/html
COPY --from=build-stage /app/src/assets /usr/share/nginx/html/src/assets
COPY --from=build-stage /app/src/common /usr/share/nginx/html/src/common

EXPOSE 8080
CMD ["nginx", "-g", "daemon off;"]
