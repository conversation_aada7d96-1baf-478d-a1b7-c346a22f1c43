import { store } from 'quasar/wrappers';
import { createPinia } from 'pinia';
import { Router } from 'vue-router';
import { storePlugin } from 'pinia-plugin-store';
import CryptoJS from 'crypto-js';


/*
 * When adding new properties to stores, you should also
 * extend the `PiniaCustomProperties` interface.
 * @see https://pinia.vuejs.org/core-concepts/plugins.html#typing-new-store-properties
 */
declare module 'pinia' {
  export interface PiniaCustomProperties {
    readonly router: Router;
  }
}

if (!process.env.KEY && process.env.ENV !== 'DEV') {
  throw new Error('No fue posible cargar la llave de cifrado.');
}

const key: string = process.env.KEY || '';

const encrypt = (value: string): string => {
  if (process.env.ENV !== 'DEV') {
    return CryptoJS.AES.encrypt(value, key).toString();
  }
  return value;
};

export const decrypt = (value: string) => {
  if (process.env.ENV !== 'DEV') {
    const bytes = CryptoJS.AES.decrypt(value, key);
    return bytes.toString(CryptoJS.enc.Utf8);
  }
  return value;
};

export default store(() => {
  const pinia = createPinia()
  const plugin = storePlugin({ stores: [{name: 'usersData' }] , encrypt, decrypt})
  pinia.use(plugin)

  // You can add Pinia plugins here
  // pinia.use(SomePiniaPlugin)

  return pinia
});
