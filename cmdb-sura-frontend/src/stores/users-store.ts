import { defineS<PERSON> } from 'pinia';
import { AuthInterface } from 'src/common/interfaces/authenticate.interface';


const defaultState: AuthInterface = {
  id: '',
  accessToken: '',
  refreshToken: '',
  name: '',
  email: '',
  givenName: '',
  familyName: '',
  username: '',
  isAuthenticated: false,
  isStaff: false,
  isSuperuser: false,
  active: false,
  idToken: '',
}

export const usersDataStore = defineStore('usersData', {
  state: () => ({...defaultState}),
  actions: {
    auth(data: AuthInterface) {
      this.$patch({...data, isAuthenticated: true})
    },
    clean() {
      this.$patch({
        ...({ ...defaultState})
      })
    }
  },
  getters: {
    getUsername(): string | null {
      return this.username
    },
    getName(): string | null {
      return this.name
    },
    getStaff(): boolean {
      return this.isStaff
    },
    getToken(): string | null {
      return this.accessToken
    },
  },
});
