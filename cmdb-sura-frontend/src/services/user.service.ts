import { AxiosResponse } from 'axios'
import BaseService from './base.service'
import { User } from 'src/common/interfaces/user.interface';

export default class UserService extends BaseService {

  public listUserService(): Promise<AxiosResponse<Array<User>>> {
    return this.get('users/')
  }

  public postUserService(formData: User): Promise<AxiosResponse<string>> {
    return this.post('users/', formData)
  }

  public putUserService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`users/${id}/`, formData)
  }

  public deleteUserService(formData: User): Promise<AxiosResponse> {
    return this.delete(`users/${formData.id}` )
  }

}