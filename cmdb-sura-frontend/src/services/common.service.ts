import { AxiosResponse } from 'axios'
import BaseService from './base.service'
import { Common, TypeDevice, FormInventoryPage } from 'src/common/interfaces/common.interface'

export default class CommonServices extends BaseService {
  getTypeDeviceService(): Promise<AxiosResponse<Array<TypeDevice>>> {
    return this.get('type_device/?status=true&is_delete=false&is_delete=false')
  }

  getBaseCityService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('base_city/?status=true&is_delete=false')
  }

  getBaseHeadquartersService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('base_headquarters/?status=true&is_delete=false')
  }

  getCompanyService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('company/?status=true&is_delete=false')
  }

  getAreaService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('area/?status=true&is_delete=false')
  }

  getModalityTagService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('modality_tag/?status=true&is_delete=false')
  }

  getManufacturerService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('manufacturer/?status=true&is_delete=false')
  }

  getStatusDeviceService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('status_device/?status=true&is_delete=false')
  }

  getInventoryService(page: number, pageSize: number, filter?: string, is_deleted?: boolean): Promise<AxiosResponse<FormInventoryPage>> {
    const searchParam = filter ? '&search=' + filter : '';
    const deletedParam = is_deleted !== undefined ? '&is_deleted=' + is_deleted : '';
    return this.get(`inventory/?page=${page}&page_size=${pageSize}${searchParam}${deletedParam}`)
  }

  postInventoryService(formData: unknown): Promise<AxiosResponse<string>> {
    return this.post('inventory/', formData)
  }

  putInventoryService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`inventory/${id}/`, formData)
  }

  exportDataService(action: string): Promise<AxiosResponse<Blob>> {
    return this.get('export/', {
      responseType: 'blob',
      params: { action: action },
    })
  }

  generateTemplateService(action: string): Promise<AxiosResponse<Blob>> {
    return this.get('generate/', {
      responseType: 'blob',
      params: { action: action },
    })
  }
}
