import { AxiosRequestConfig, AxiosResponse } from 'axios'
import { api } from 'src/boot/axios'

export default class BaseService {
  protected get<T>(
    url: string,
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse<T>> {
    return api.get(url, config)
  }

  protected post<T>(
    url: string,
    params: unknown = {},
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse<T>> {
    return api.post(url, params, config)
  }

  protected put<T>(
    url: string,
    params: unknown = {},
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse<T>> {
    return api.put(url, params, config)
  }

  protected patch<T>(
    url: string,
    params: unknown = {},
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse<T>> {
    return api.patch(url, params, config)
  }

  protected delete<T>(
    url: string,
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse<T>> {
    return api.delete(url, config)
  }
}
