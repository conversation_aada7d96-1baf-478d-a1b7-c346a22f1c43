import { AxiosRequestConfig, AxiosResponse } from 'axios'
import { powerbiApi } from 'src/boot/axios'
import { PowerBITokenResponse } from 'src/common/interfaces/common.interface';

export default class BITokenService {
  public post(
    params: unknown = {},
    config: AxiosRequestConfig = {}
  ): Promise<AxiosResponse<PowerBITokenResponse>> {
    return powerbiApi.post('/token/', params, config)
  }
}