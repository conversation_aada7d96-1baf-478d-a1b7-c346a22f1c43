import { AxiosResponse } from 'axios'
import BaseService from './base.service'
import { Common, TypeDevice, FormInventory } from 'src/common/interfaces/common.interface'

export default class AdminServices extends BaseService {

  getTypeDeviceService(): Promise<AxiosResponse<Array<TypeDevice>>> {
    return this.get('type_device/?is_delete=false')
  }

  postTypeDeviceService(formData: TypeDevice): Promise<AxiosResponse<string>> {
    return this.post('type_device/', formData)
  }

  putTypeDeviceService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`type_device/${id}/`, formData)
  }

  deleteTypeDeviceService(formData: TypeDevice): Promise<AxiosResponse> {
    return this.put(`type_device/${formData.id}/`, {...formData, isDelete: true})
  }

  getBaseCityService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('base_city/?is_delete=false')
  }

  postBaseCityService(formData: Common): Promise<AxiosResponse<string>> {
    return this.post('base_city/', formData)
  }

  putBaseCityService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`base_city/${id}/`, formData)
  }

  deleteBaseCityService(formData: Common): Promise<AxiosResponse> {
    return this.put(`base_city/${formData.id}/`, {...formData, isDelete: true})
  }

  getBaseHeadquartersService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('base_headquarters/?is_delete=false')
  }

  postBaseHeadquartersService(formData: Common): Promise<AxiosResponse<string>> {
    return this.post('base_headquarters/', formData)
  }

  putBaseHeadquartersService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`base_headquarters/${id}/`, formData)
  }

  deleteBaseHeadquartersService(formData: Common): Promise<AxiosResponse> {
    return this.put(`base_headquarters/${formData.id}/`, {...formData, isDelete: true})
  }

  getCompanyService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('company/?is_delete=false')
  }

  postCompanyService(formData: Common): Promise<AxiosResponse<string>> {
    return this.post('company/', formData)
  }

  putCompanyService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`company/${id}/`, formData)
  }

  deleteCompanyService(formData: Common): Promise<AxiosResponse> {
    return this.put(`company/${formData.id}/`, {...formData, isDelete: true})
  }

  getAreaService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('area/?is_delete=false')
  }

  postAreaService(formData: Common): Promise<AxiosResponse<string>> {
    return this.post('area/', formData)
  }

  putAreaService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`area/${id}/`, formData)
  }

  deleteAreaService(formData: Common): Promise<AxiosResponse> {
    return this.put(`area/${formData.id}/`, {...formData, isDelete: true})
  }

  getModalityTagService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('modality_tag/?is_delete=false')
  }

  postModalityTagService(formData: Common): Promise<AxiosResponse<string>> {
    return this.post('modality_tag/', formData)
  }

  putModalityTagService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`modality_tag/${id}/`, formData)
  }

  deleteModalityTagService(formData: Common): Promise<AxiosResponse> {
    return this.put(`modality_tag/${formData.id}/`, {...formData, isDelete: true})
  }

  getManufacturerService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('manufacturer/?is_delete=false')
  }

  postManufacturerService(formData: Common): Promise<AxiosResponse<string>> {
    return this.post('manufacturer/', formData)
  }

  putManufacturerService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`manufacturer/${id}/`, formData)
  }

  deleteManufacturerService(formData: Common): Promise<AxiosResponse> {
    return this.put(`manufacturer/${formData.id}/`, {...formData, isDelete: true})
  }

  getStatusDeviceService(): Promise<AxiosResponse<Array<Common>>> {
    return this.get('status_device/?is_delete=false')
  }

  postStatusDeviceService(formData: Common): Promise<AxiosResponse<string>> {
    return this.post('status_device/', formData)
  }

  putStatusDeviceService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`status_device/${id}/`, formData)
  }

  deleteStatusDeviceService(formData: Common): Promise<AxiosResponse> {
    return this.put(`status_device/${formData.id}/`, {...formData, isDelete: true})
  }

  getInventoryService(): Promise<AxiosResponse<Array<FormInventory>>> {
    return this.get('inventory/')
  }
  postInventoryService(formData: unknown): Promise<AxiosResponse<string>> {
    return this.post('inventory/', formData)
  }
  putInventoryService(id: number, formData: unknown): Promise<AxiosResponse<string>> {
    return this.put(`inventory/${id}/`, formData)
  }
  deleteInventoryService(formData: FormInventory, TypeDeviceId: number): Promise<AxiosResponse> {
    return this.put(`inventory/${formData?.id}/`, {...formData, isDelete: true, typeDevice: TypeDeviceId})
  }
}