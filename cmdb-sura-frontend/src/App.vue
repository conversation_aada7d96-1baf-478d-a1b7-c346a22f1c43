<template>
  <router-view />
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { usersDataStore } from './stores/users-store';

defineOptions({
  name: 'App'
});

const authStore = usersDataStore();
const router = useRouter();
authStore.$subscribe(
  (mutation) => {
    if ('payload' in mutation) {
      if ('isAuthenticated' in mutation.payload) {
        router
          .replace({
            name: mutation.payload.isAuthenticated ? 'index' : 'Verify',
          })
          .catch(() => null);
      }
    }
  },
  { detached: true }
);
</script>