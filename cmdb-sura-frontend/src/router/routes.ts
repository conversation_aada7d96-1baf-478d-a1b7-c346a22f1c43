import { RouteRecordRaw } from 'vue-router';


const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('layouts/AuthLayout.vue'),
    children: [{ path: '', component: () => import('pages/LoginVerifyPage.vue'), name:'Verify'}],
    meta: { requireAuth: false }
  },
  {
    path: '/logout',
    component: () => import('layouts/AuthLayout.vue'),
    children: [{ path: '', component: () => import('pages/LogoutPage.vue'), name:'logout'}],
  },
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    meta: { requireAuth: true },
    children: [
      { path: 'index', component: () => import('pages/IndexPage.vue'), name:'index' },
      { path: 'users', component: () => import('pages/admin/UsersAdminPage.vue'), name:'Users' },
      { path: 'type_device', component: () => import('pages/admin/TypeDevicePage.vue'), name:'TypeDevice' },
      { path: 'base_city', component: () => import('pages/admin/BaseCityPage.vue'), name:'BaseCity' },
      { path: 'base_headquarters', component: () => import('pages/admin/BaseHeadquartersPage.vue'), name:'BaseHeadquarters' },
      { path: 'company', component: () => import('pages/admin/CompanyPage.vue'), name:'Company' },
      { path: 'area', component: () => import('pages/admin/AreaPage.vue'), name:'Area' },
      { path: 'modality_tag', component: () => import('pages/admin/ModalityTagPage.vue'), name:'ModalityTag' },
      { path: 'manufacturer', component: () => import('pages/admin/ManufacturerPage.vue'), name:'Manufacturer' },
      { path: 'status_device', component: () => import('pages/admin/StatusDevicePage.vue'), name:'StatusDevice' },
      { path: 'reports', component: () => import('pages/ReportPage.vue'), name:'Reports' },
    ],   
  },
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;