import { route } from 'quasar/wrappers'
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory
} from 'vue-router'

import routes from './routes'
/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

const validateHistory = () => {
  return process.env.VUE_ROUTER_MODE === 'history'
    ? createWebHistory
    : createWebHashHistory
}

export default route(({ store }) => {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : validateHistory()

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE)
  })

  Router.beforeEach((to, from, next) => {
    const userAuthenticated =
      store.state.value['usersData'].isAuthenticated ??
      false
    const token =
      store.state.value['usersData'].accessToken ??
      undefined
    const passAuthentication = userAuthenticated && !!token
    if (to.meta.requireAuth && !passAuthentication) {
      next({ name: 'Verify', query: { next: to.fullPath } })
    } else if (to.path === '/' && passAuthentication) {
      next('/index')
    } else {
      next()
    }
  })

  return Router
})
