<template>
  <q-layout view="hHh Lpr lff">
    <q-header elevated>
      <q-toolbar>
        <q-btn flat dense icon="menu" class="q-mr-sm" @click="drawer = !drawer" />
        <q-toolbar-title >
          <q-img src="/sura-logo-blanco.png" class="" width="80px" spinner-color="primary" spinner-size="82px"/>
          <q-chip dark size="xs" color="primary">
              Powered by
              <q-img src="/Logo-ARUS.webp" class="q-ma-xs" width="30px" spinner-color="primary" spinner-size="82px" />
          </q-chip>          
        </q-toolbar-title>
        <span class="text-overload">{{ UsersData.getName }}</span>
        <q-btn :icon="$q.dark.isActive ? 'light_mode' : 'dark_mode'" class="q-mx-sm" @click="$q.dark.toggle()" flat>
          <q-tooltip>{{ $q.dark.isActive ? 'Modo claro' : 'Modo oscuro' }}</q-tooltip>
        </q-btn>
        <q-btn color="white" icon="logout" flat @click="logout()">
          <q-tooltip>Cerrar sesión</q-tooltip>
        </q-btn>
      </q-toolbar>
    </q-header>
    <q-drawer 
      v-model="drawer"
      show-if-above
      :width="200"
      :breakpoint="500"
      bordered
      :class="$q.dark.isActive ? 'bg-grey-9' : 'bg-grey-3'"
      :mini="miniState"
      @mouseenter="miniState = false"
      @mouseleave="miniState = true"
    >
      <q-list>
        <q-item clickable v-ripple :to="{name: data.path}" active-class="text-secondary" class="text-primary" v-for="data in menuItems" :key="data.label" >
          <q-item-section avatar dark >
            <q-icon :name="data.icon" />
          </q-item-section>
          <q-item-section>
            {{ data.label }}
          </q-item-section>
          <q-tooltip>
            {{ data.label }}
          </q-tooltip>
        </q-item>
      </q-list>
    </q-drawer>
    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { usersDataStore } from 'src/stores/users-store'
import { onMounted, ref } from 'vue';
import { adminMenuItems, EmptyMenuItem, MenuItem } from 'src/common/consts/admin';

defineOptions({
  name: 'MainLayout'
});

const UsersData = usersDataStore()
const drawer = ref<boolean>(false);
const $q = useQuasar();
const staff = ref<boolean>(UsersData.getStaff);
const menuItems = ref<MenuItem[]>(EmptyMenuItem);
const miniState = ref(true);

onMounted(() => {  
  getMenuItems()
})

function getMenuItems(){
  menuItems.value = staff.value ? adminMenuItems : adminMenuItems.filter(item => item.admin === staff.value)
}

function logout(): void {

  $q.dialog({
    title: '¿Finalizar sesión?',
    message: '¿Está seguro de que desea finalizar la sesión activa?',
    cancel: {
      label: 'Cancelar',
      flat: true
    },
    ok: {
      label: 'Finalizar sesión'
    },
    persistent: true
  }).onOk(() => {
    const id_token = UsersData?.idToken ?? '';
    const redirectUrl = `${process.env.FRONT_URL ?? ''}logout`;
    const url =
      `${process.env.KEYCLOAK_URL ?? ''}realms/${process.env.KEYCLOAK_REALM ?? ''}` +
      `/protocol/openid-connect/logout?post_logout_redirect_uri=${redirectUrl}` +
      `&client_id=${process.env.KEYCLOAK_CLIENT ?? ''}&id_token_hint=${id_token}`;
    window.location.href = url;
  })
}
</script>
