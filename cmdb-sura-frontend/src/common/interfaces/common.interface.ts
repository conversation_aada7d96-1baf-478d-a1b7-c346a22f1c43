import { AxiosResponse } from 'axios';


export interface Common {
  id?: number | null,
  name: string
  status: boolean
}

export interface TypeDevice {
  deviceClass: string,
  id?: number | null,
  name: string,
  years: number,
  status: boolean,
  icon?: string | null
}

export interface FormInventory {
  id?: number | null,
  baseCity: number | null,
  baseHeadquarters: number | null,
  location: string,
  document: string,
  company: number | null,
  username: string,
  typeDevice: TypeDevice | null,
  typeDeviceName: string,
  area: number | null,
  floor: string,
  manufacturer: number | null,
  model: string,
  serial: string,
  licensePlate: string,
  ownership: string,
  warehouseEntryDate: string,
  networkUser: string,
  modality: string,
  modalityTag: number | null,
  comments: string,
  status: number | null,
  machineName: string,
  warrantyDate: string,
  os: string,
  processorAndSpeed: string,
  hardDrive: string,
  memory: string,
  totalMemoryBanks: string,
  usedBanks: string,
  freeBanks: string,
  technologyRenewalDate: string,
  typeDeviceIcon?: string | null,
  deleteCause?: string | null,
  isDelete?: boolean
}

export interface FormInventoryPage {
  count: number,
  next: string,
  previous: string,
  results: FormInventory[]
}

export const DefaultValuesInventory: FormInventory = {
  baseCity: null,
  baseHeadquarters: null,
  location: '',
  document: '',
  company: null,
  username: '',
  typeDevice: null,
  typeDeviceName: '',
  area: null,
  floor: '',
  manufacturer: null,
  model: '',
  serial: '',
  licensePlate: '',
  ownership: '',
  warehouseEntryDate: '',
  networkUser: '',
  modality: '',
  modalityTag: null,
  comments: '',
  status: null,
  machineName: '',
  warrantyDate: '',
  os: '',
  processorAndSpeed: '',
  hardDrive: '',
  memory: '',
  totalMemoryBanks: '',
  usedBanks: '',
  freeBanks: '',
  technologyRenewalDate: '',
  deleteCause: '',
};

export interface ResponseError{
  cell: number,
  error: string
}

export interface ResponseData{
  success: boolean,
  message: string,
  totalProcessed: number,
  validRegisters: number,
  registersWithErrors: number,
  errors: ResponseError[]

}

export const DefaultResponseData: ResponseData = {
  success: false,
  message: '',
  totalProcessed: 0,
  validRegisters: 0,
  registersWithErrors: 0,
  errors: [{cell: 0, error: ''}]

}

export interface BaseService{
  getService: () => Promise<AxiosResponse<Array<Common>>>,
  postService: (data: Common) => Promise<AxiosResponse<string>>,
  putService: (id: number, data: unknown) => Promise<AxiosResponse<string>>,
  deleteService: (data: Common) => Promise<AxiosResponse>,
}

export interface PowerBITokenResponse {
  token: string
}