export interface MenuItem {
  label: string;
  icon: string;
  path: string;
  admin: boolean;
}

const createAdminMenuItem = (label: string, icon: string, path: string, admin = true): MenuItem => ({
  label,
  icon,
  path,
  admin,
});

export const adminMenuItems: MenuItem[] = [
  createAdminMenuItem('Inventario', 'post_add', 'index', false),
  createAdminMenuItem('Administrar usuarios', 'manage_accounts', 'Users'),
  createAdminMenuItem('Tipo de dispositivo', 'devices', 'TypeDevice'),
  createAdminMenuItem('Ciudad base', 'location_city', 'BaseCity'),
  createAdminMenuItem('Sede base', 'domain', 'BaseHeadquarters'),
  createAdminMenuItem('Compañía', 'business_center', 'Company'),
  createAdminMenuItem('Área', 'storefront', 'Area'),
  createAdminMenuItem('Etiqueta de Modalidad', 'sell', 'ModalityTag'),
  createAdminMenuItem('Fabricante', 'construction', 'Manufacturer'),
  createAdminMenuItem('Estados de dispositivo', 'info', 'StatusDevice'),
  createAdminMenuItem('Reportes', 'bar_chart', 'Reports', false),
];

export const EmptyMenuItem: MenuItem[] = [createAdminMenuItem('', '', '', false)]
