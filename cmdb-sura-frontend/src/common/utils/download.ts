import { QVueGlobals } from 'quasar';
import { AxiosResponse } from 'axios';

export function downloadExcelFile(response: AxiosResponse, $q: QVueGlobals, filename = 'inventario.xlsx') {
  try {
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    $q.notify({ type: 'positive', position: 'top', message: 'Se ha generado el archivo exitosamente' });
  } catch {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al descargar el archivo.' });
  }
}