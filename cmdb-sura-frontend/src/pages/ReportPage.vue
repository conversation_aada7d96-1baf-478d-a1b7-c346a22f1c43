<template>
  <q-responsive :ratio="16/9">
    <div class="col" ref="reportContainer"></div>
   </q-responsive>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { models, type IReportEmbedConfiguration } from 'powerbi-client';
import * as powerbi from 'powerbi-client';
import { useQuasar } from 'quasar';
import BITokenService from 'src/services/bitoken.service';

const $q = useQuasar();
const reportContainer = ref<HTMLElement>();
const reportConfig = ref<IReportEmbedConfiguration>({} as IReportEmbedConfiguration);

onMounted(() => {
    embedReport()
})

function embedReport(): void {
  const tokenService: BITokenService = new BITokenService();
  tokenService.post().then((response) => {
    const powerbiService = new powerbi.service.Service(
      powerbi.factories.hpmFactory,
      powerbi.factories.wpmpFactory,
      powerbi.factories.routerFactory
    )
    reportConfig.value = {
      type: 'report',
      id: process.env.POWERBI_REPORT_ID,
      embedUrl: process.env.POWERBI_EMBED_URL,
      tokenType: models.TokenType.Aad,
      accessToken: response.data.token,
      settings: undefined,
    }
    if (reportContainer.value) {
      powerbiService.embed(reportContainer.value, reportConfig.value)
    }
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al obtener el token de Power BI.' })
  });
}

</script>
