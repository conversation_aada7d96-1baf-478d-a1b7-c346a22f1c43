<template>
  <div class="q-pa-xs column">
    <q-responsive :ratio="16/9">
    <q-table flat bordered grid :rows="rows" row-key="serial" :filter="filter" rows-per-page-label="Activos por página" v-model:pagination="pagination" :loading="isLoading" @request="onRequest" hide-header>
      <template v-slot:top-left>
        <q-tabs
          v-model="tab"
          dense
          class="text-grey"
          active-color="primary"
          indicator-color="primary"
          align="justify"
          narrow-indicator
          @update:model-value="onTabChange"
        >
          <q-tab name="inventory" label="Inventario" />
          <q-tab name="deleted" label="Dados de baja" />
        </q-tabs>
      </template>
      <template v-slot:top-right>        
        <q-btn color="primary" flat icon="download" @click="exportData()" :loading="loading">
          <q-tooltip>Exportar</q-tooltip>
        </q-btn>
        <q-separator spaced inset vertical v-show="staff"/>
        <q-btn-dropdown color="primary" flat icon="add" v-show="staff">
          <q-list>
            <q-item clickable v-close-popup @click="viewRegister = true" v-show="tab === 'inventory'">
              <q-item-section top avatar>
                <q-avatar icon="playlist_add" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Agregar registro individual</q-item-label>
              </q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="loadMassive = true">
              <q-item-section top avatar>
                <q-avatar icon="upload_file" />
              </q-item-section>
              <q-item-section>
                <q-item-label v-show="tab === 'inventory'">Agregar por carga masiva</q-item-label>
                <q-item-label v-show="tab === 'deleted'">Eliminar por carga masiva</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-btn-dropdown>
        <q-separator spaced inset vertical />
        <q-input borderless dense debounce="300" v-model="filter" placeholder="Buscar">
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </template>
      <template v-slot:item="props">
        <q-tab-panels v-model="tab" animated >
          <q-tab-panel name="inventory">
            <q-btn-dropdown class="q-my-md" flat split push @click="viewDetail(props.row)">
              <template v-slot:label>
                <div class="row q-pa-sm items-center justify-between">
                  <div class="q-ma-xs">
                    <q-avatar :icon="props.row.typeDeviceIcon" text-color="deep-purple-10" >
                      <q-tooltip>{{ props.row.typeDeviceName }}</q-tooltip>
                    </q-avatar>
                  </div>
                  <div class="text-center">
                    <q-item-label class="text-h6 text-secondary" style="overflow: hidden; width: 8em; text-overflow: ellipsis;">{{ props.row.serial }}</q-item-label>
                    <q-item-label caption>{{ props.row.machineName }}</q-item-label>
                  </div>
                </div>
              </template>
              <q-card class="my-card column" flat bordered>
                <q-card-section horizontal>
                  <q-card-section>
                    <q-list separator>
                      <q-item>
                        <q-item-section top avatar>
                          <q-icon color="primary" name="location_city" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label class="text-weight-regular text-caption" style="overflow: hidden; width: 6em; text-overflow: ellipsis;">{{ props.row.cityName }}</q-item-label>
                          <q-item-label caption style="overflow: hidden; width: 6em; text-overflow: ellipsis;">Ciudad</q-item-label>
                        </q-item-section>
                      </q-item>
                      <q-item>
                        <q-item-section top avatar>
                          <q-icon color="primary" name="domain" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label class="text-weight-regular text-caption" style="overflow: hidden; width: 6em; text-overflow: ellipsis;">{{ props.row.baseHeadquartersName }}</q-item-label>
                          <q-item-label caption style="overflow: hidden; width: 6em; text-overflow: ellipsis;">Sede</q-item-label>
                        </q-item-section>
                      </q-item>
                      <q-item>
                        <q-item-section top avatar>
                          <q-icon color="primary" name="explore" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label class="text-weight-regular text-caption" style="overflow: hidden; width: 6em; text-overflow: ellipsis;" >{{ props.row.licensePlate }}</q-item-label>
                          <q-item-label caption style="overflow: hidden; width: 5em; text-overflow: ellipsis;">Placa</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </q-card-section>
                  <q-separator vertical />
                  <q-card-section>
                    <q-list separator>
                      <q-item>
                        <q-item-section top avatar>
                          <q-icon color="primary" name="style" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label class="text-weight-regular text-caption" style="overflow: hidden; width: 6em; text-overflow: ellipsis;">{{ props.row.model }}</q-item-label>
                          <q-item-label caption style="overflow: hidden; width: 6em; text-overflow: ellipsis;">Modelo</q-item-label>
                        </q-item-section>
                      </q-item>
                      <q-item>
                        <q-item-section top avatar>
                          <q-icon color="primary" name="notification_important" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label class="text-weight-regular text-caption" style="overflow: hidden; width: 6em; text-overflow: ellipsis;">{{ props.row.statusName }}</q-item-label>
                          <q-item-label caption style="overflow: hidden; width: 6em; text-overflow: ellipsis;">Estado del dispositivo</q-item-label>
                        </q-item-section>
                      </q-item>
                      <q-item>
                        <q-item-section top avatar>
                          <q-icon size="xs" color="primary" name="branding_watermark" />
                        </q-item-section>
                        <q-item-section no-wrap >
                          <q-item-label class="text-weight-regular text-caption" style="overflow: hidden; width: 6em; text-overflow: ellipsis;">{{ props.row.ownership }}</q-item-label>
                          <q-item-label caption>Propiedad</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </q-card-section>
                </q-card-section>
                <q-separator />
                <q-card-actions align="center" vertical class="q-pa-sm col">
                  <q-btn  @click="viewDetail(props.row)" color="secondary" flat icon="edit" label="Editar" v-show="staff"/>
                  <q-btn  @click="viewDetail(props.row)" color="secondary" flat icon="visibility" label="Ver" v-show="!staff"/>
                </q-card-actions>
              </q-card>
            </q-btn-dropdown>
          </q-tab-panel>
          <q-tab-panel name="deleted" >
            <q-btn-dropdown class="q-my-md" flat split push @click="viewDetail(props.row)">
              <template v-slot:label>
                <div class="row q-pa-sm items-center justify-between">
                  <div class="q-ma-xs">
                    <q-avatar :icon="props.row.typeDeviceIcon" text-color="deep-purple-10" >
                      <q-tooltip>{{ props.row.typeDeviceName }}</q-tooltip>
                    </q-avatar>
                  </div>
                  <div class="text-center">
                    <q-item-label class="text-h6 text-secondary" style="overflow: hidden; width: 8em; text-overflow: ellipsis;">{{ props.row.serial }}</q-item-label>
                    <q-item-label caption>{{ props.row.machineName }}</q-item-label>
                  </div>
                </div>
              </template>
              <q-card class="my-card column" flat bordered>
                <q-card-section horizontal>
                  <q-card-section>
                    <q-list separator>
                      <q-item>
                        <q-item-section top avatar>
                          <q-icon color="primary" name="notification_important" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label class="text-weight-regular text-caption" style="overflow: hidden; width: 6em; text-overflow: ellipsis;">{{ props.row.deleteCause }}</q-item-label>
                          <q-item-label caption style="overflow: hidden; width: 6em; text-overflow: ellipsis;">Causal de baja</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </q-card-section>
                </q-card-section>
                <q-separator />
                <q-card-actions align="center" vertical class="q-pa-sm col">
                  <q-btn  @click="viewDetail(props.row)" color="secondary" flat icon="edit" label="Editar" v-show="staff && tab === 'inventory'"/>
                  <q-btn  @click="viewDetail(props.row)" color="secondary" flat icon="visibility" label="Ver" v-show="!staff || tab === 'deleted'"/>
                </q-card-actions>
              </q-card>
            </q-btn-dropdown>
          </q-tab-panel>
        </q-tab-panels>
      </template>
    </q-table>
    <q-dialog v-model="viewRegister" full-width persistent>
      <q-card>
        <q-card-section>
          <q-btn class="float-right" icon="close" flat v-close-popup />
          <div class="text-h6">Agregar elementos al inventario</div>
        </q-card-section>
        <q-card-section>
          <RegisterInventoryComponent @closeModal="refreshData()" :tab="tab"/>
        </q-card-section>
      </q-card>
    </q-dialog>
    <q-dialog v-model="loadMassive" persistent>
      <q-card >
        <q-card-section>
          <q-btn class="float-right" icon="close" flat v-close-popup />
          <div class="text-h6" v-show="tab === 'inventory'">Cargar inventario de forma masiva</div>
          <div class="text-h6" v-show="tab === 'deleted'">Eliminar inventario de forma masiva</div>
        </q-card-section>
        <q-card-section class="q-pa-xs">
          <MassiveLoadInventoryComponent @close-massive-load="closeLoadMassive()" @closeModal="refreshData()" :tab="tab"/>
        </q-card-section>
      </q-card>
    </q-dialog>
    <q-dialog v-model="viewDetailData" full-width persistent>
      <q-card>
        <q-card-section>
          <q-btn class="float-right" icon="close" flat v-close-popup />
          <div class="text-h6">Editar elemento</div>
        </q-card-section>
        <q-card-section>
          <RegisterInventoryComponent :dataInventory="detailData" @closeModal="refreshData()" :tab="tab"/>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-responsive>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import CommonServices from 'src/services/common.service'
import { useQuasar, QTableProps } from 'quasar'
import { FormInventory, DefaultValuesInventory, TypeDevice } from 'src/common/interfaces/common.interface'
import RegisterInventoryComponent from 'src/components/RegisterInventoryComponent.vue'
import MassiveLoadInventoryComponent from 'src/components/MassiveLoadInventoryComponent.vue'
import { usersDataStore } from 'src/stores/users-store'
import { downloadExcelFile } from 'src/common/utils/download';

const $q = useQuasar();
const filter = ref<string>('')
const viewRegister = ref<boolean>(false)
const viewDetailData = ref<boolean>(false)
const servicesList: CommonServices = new CommonServices()
const rows = ref<FormInventory[]>([])
const detailData = ref<FormInventory>({ ...DefaultValuesInventory })
const usersData = usersDataStore()
const staff = ref<boolean>(usersData.getStaff);
const loadMassive = ref<boolean>(false);
const typeDeviceList = ref<TypeDevice[]>([]);
const loading = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const defaultPage = 1;
const defaultRowsPerPage = 15;
const tab = ref<string>('inventory');
const pagination = ref<QTableProps['pagination']>({
  sortBy: 'desc',
  descending: false,
  page: defaultPage,
  rowsPerPage: defaultRowsPerPage,
  rowsNumber: 100
})

onMounted(() => {
  listInventory();
})

function refreshData(): void {
  listInventory();
  viewRegister.value = false;
  viewDetailData.value = false;
}

function viewDetail(data: FormInventory): void {
  viewDetailData.value =  true;
  detailData.value =  data
}

function listInventory(): void {
  const pageSize: number = (pagination.value?.rowsPerPage || defaultRowsPerPage)
  const page : number = pagination.value?.page ?? defaultPage
  let isDeleted = false
  if (tab.value === 'deleted') {
    isDeleted = true
  }
  servicesList.getInventoryService(page, pageSize, filter.value, isDeleted).then((response) => {
    rows.value = response.data.results;
    if (pagination.value){
      pagination.value.rowsNumber = response.data.count
    }
    getTypeDeviceList()
    isLoading.value = false;
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al listar tipos de dispositivos.' })
  });

}

function exportData(): void {
  loading.value = true;
  const action = tab.value === 'inventory' ? 'create' : 'delete';
  servicesList.exportDataService(action).then((response) => {
    downloadExcelFile(response, $q);
    loading.value = false;
  }).catch(() => {
    loading.value = false;
    $q.notify({ type: 'negative', position: 'top', message: 'Error al descargar el archivo.' });
  });
}

function closeLoadMassive(): void{
  loadMassive.value = false
}

function getTypeDeviceList(): void {  
  servicesList.getTypeDeviceService().then((response) => {
    typeDeviceList.value = response.data
    rows.value = rows.value.map(row => {
      row.typeDeviceName = typeDeviceList.value?.find((device) => device.id == row.typeDevice)?.name || '';
      return row
    });
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al crear lista de tipos de dispositivo.' })
  });
}

function onRequest(props: { pagination: QTableProps['pagination'] }): void {
  isLoading.value = true;
  if (pagination.value && props.pagination) {
    const page: number | undefined = props.pagination.page
    const rowsPerPage: number | undefined = props.pagination.rowsPerPage
    const rowsNumber: number | undefined = props.pagination.rowsNumber
    if (page && rowsPerPage && rowsNumber) {
          pagination.value.page = page
          pagination.value.rowsPerPage = rowsPerPage
          pagination.value.rowsNumber = rowsNumber
    }
  }
  listInventory();
}

function onTabChange(): void {
  if (pagination.value) {
    pagination.value.page = defaultPage;
  }
  listInventory();
}
</script>