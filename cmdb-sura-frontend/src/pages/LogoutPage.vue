<template>
  <div>
    <q-card>
      <q-card-section>
        {{ TextResource.LOGOUT_SUCCESS }}
      </q-card-section>
    </q-card>
  </div>
</template>
<script setup lang="ts">
import { TextResource } from 'src/common/enums/text.enum';
import { usersDataStore } from 'src/stores/users-store';
import { onMounted } from 'vue';

const store = usersDataStore();

onMounted(() => {
  store.clean();
});

</script>