<template>
  <BaseAdminComponent
    title="Sede base"
    subtitle="Agregar sede base"
    :getService="getBaseHeadquartersService"
    :postService="postBaseHeadquartersService"
    :putService="putBaseHeadquartersService"
    :deleteService="deleteBaseHeadquartersService"
  />
</template>

<script setup lang="ts">
import BaseAdminComponent from 'src/components/admin/BaseAdminComponent.vue';
import AdminServices from 'src/services/admin.service';
import { Common } from 'src/common/interfaces/common.interface';

const servicesList = new AdminServices();
const getBaseHeadquartersService = () => servicesList.getBaseHeadquartersService();
const postBaseHeadquartersService = (data: Common) => servicesList.postBaseHeadquartersService(data);
const putBaseHeadquartersService = (id: number, data: unknown) => servicesList.putBaseHeadquartersService(id, data);  
const deleteBaseHeadquartersService = (data: Common) => servicesList.deleteBaseHeadquartersService(data);

</script>