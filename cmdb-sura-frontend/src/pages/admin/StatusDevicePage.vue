<template>
  <BaseAdminComponent
    title="Estados de dispositivo"
    subtitle="Agregar estado de dispositivo"
    :getService="getStatusDeviceService"
    :postService="postStatusDeviceService"
    :putService="putStatusDeviceService"
    :deleteService="deleteStatusDeviceService"
  />
</template>

<script setup lang="ts">
import BaseAdminComponent from 'src/components/admin/BaseAdminComponent.vue';
import AdminServices from 'src/services/admin.service';
import { Common } from 'src/common/interfaces/common.interface';

const servicesList = new AdminServices();
const getStatusDeviceService = () => servicesList.getStatusDeviceService();
const postStatusDeviceService = (data: Common) => servicesList.postStatusDeviceService(data);
const putStatusDeviceService = (id: number, data: unknown) => servicesList.putStatusDeviceService(id, data);  
const deleteStatusDeviceService = (data: Common) => servicesList.deleteStatusDeviceService(data);

</script>