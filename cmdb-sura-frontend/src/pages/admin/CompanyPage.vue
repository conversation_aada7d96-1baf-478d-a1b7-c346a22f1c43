<template>
  <BaseAdminComponent
    title="Compañias"
    subtitle="Agregar compañia"
    :getService="getCompanyService"
    :postService="postCompanyService"
    :putService="putCompanyService"
    :deleteService="deleteCompanyService"
  />
</template>

<script setup lang="ts">
import BaseAdminComponent from 'src/components/admin/BaseAdminComponent.vue';
import AdminServices from 'src/services/admin.service';
import { Common } from 'src/common/interfaces/common.interface';

const servicesList = new AdminServices();
const getCompanyService = () => servicesList.getCompanyService();
const postCompanyService = (data: Common) => servicesList.postCompanyService(data);
const putCompanyService = (id: number, data: unknown) => servicesList.putCompanyService(id, data);  
const deleteCompanyService = (data: Common) => servicesList.deleteCompanyService(data);

</script>