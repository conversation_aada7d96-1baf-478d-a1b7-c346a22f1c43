<template>
  <BaseAdminComponent
    title="Etiquetas de modalidad"
    subtitle="Agregar etiqueta de modalidad"
    :getService="getModalityTagService"
    :postService="postModalityTagService"
    :putService="putModalityTagService"
    :deleteService="deleteModalityTagService"
  />
</template>

<script setup lang="ts">
import BaseAdminComponent from 'src/components/admin/BaseAdminComponent.vue';
import AdminServices from 'src/services/admin.service';
import { Common } from 'src/common/interfaces/common.interface';

const servicesList = new AdminServices();
const getModalityTagService = () => servicesList.getModalityTagService();
const postModalityTagService = (data: Common) => servicesList.postModalityTagService(data);
const putModalityTagService = (id: number, data: unknown) => servicesList.putModalityTagService(id, data);  
const deleteModalityTagService = (data: Common) => servicesList.deleteModalityTagService(data);

</script>