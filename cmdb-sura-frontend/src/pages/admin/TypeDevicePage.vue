<template>
  <q-page padding>
    <q-table flat bordered title="Tipos de dispositivo" :rows="rows" row-key="name" :filter="filter"
      :columns="columns">
      <template v-slot:top-right>
        <q-btn color="primary" label="Agregar" @click="modalAdd()" icon="add" />
      </template>
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <q-btn color="info" icon="edit" @click="modalEdit(props.row)" dense flat>
            <q-tooltip>
              Editar
            </q-tooltip>
          </q-btn>
          <q-btn color="negative" icon="delete" @click="deleteRow(props.row)" dense flat>
            <q-tooltip>
              Eliminar
            </q-tooltip>
          </q-btn>
        </q-td>
      </template>
    </q-table>
    <q-dialog v-model="modal" persistent :backdrop-filter="'blur(4px) saturate(150%)'">
      <q-card style="width: 300px">
        <q-card-section>
          <q-btn flat dense icon="close" class="float-right" v-close-popup />
          <span class="text-subtitle1">Agregar tipo de dispositivo</span>
        </q-card-section>
        <q-card-section>
          <q-form class="q-py-md" @submit="editMode ? editRow() : save() " greedy>
            <q-input v-model="form.name" type="text" label="Nombre" reactive-rules
              :rules="[val => !!val || 'Este campo es obligatorio']" />
            <q-select v-model="form.deviceClass" :options="options" label="Clase de dispositivo" reactive-rules
            :rules="[val => !!val || 'Este campo es obligatorio']"/>
            <q-input v-model="form.years" type="text" label="Años para vencimiento" reactive-rules
              :rules="[val => !!val || 'Este campo es obligatorio']" />
            <q-checkbox left-label v-model="form.status" label="Está activo?" v-if="editMode" />
            <q-input v-model="form.icon" type="text" label="Icono" reactive-rules />
            <div class="text-center">
              <q-btn color="primary" class="q-mt-md" icon="save" :label="editMode ? 'Actualizar' : 'Guardar'"
                type="submit" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import AdminServices from 'src/services/admin.service';
import { TypeDevice } from 'src/common/interfaces/common.interface';
import { useQuasar, QTableProps } from 'quasar';

const $q = useQuasar()
const form = ref<TypeDevice>({ name: '', status: true, years: 0, deviceClass: '' });
const servicesList = new AdminServices()
const rows = ref<TypeDevice[]>([])
const filter = ref<string>('')
const options: string[] = [
  'Movil', 'Fijo'
]
const editMode = ref<boolean>(false)
const modal = ref<boolean>(false)
const columns: QTableProps['columns'] = [
  {
    name: 'actions',
    label: 'Acciones',
    align: 'left',
    field: '',
  },
  {
    name: 'name',
    required: true,
    label: 'Nombre',
    align: 'left',
    field: (row: TypeDevice) => row.name,
    sortable: true,
  },
  {
    name: 'status',
    label: 'Estado',
    align: 'left',
    field: (row: TypeDevice) => row.status ? 'Activo' : 'Inactivo',
    sortable: true,
  },
  {
    name: 'icon',
    label: 'Icono',
    align: 'left',
    field: (row: TypeDevice) => row.icon,
    sortable: true,
  },
];

onMounted(() => {
  listTypeDevice();
})

function modalAdd(): void {
  modal.value = true
  form.value = { name: '', status: true, years: 0, deviceClass: '' }
  editMode.value = false
}

function modalEdit(data: TypeDevice): void {
  modal.value = true
  form.value = data
  editMode.value = true
}

function listTypeDevice(): void {
  servicesList.getTypeDeviceService().then((response) => {
    rows.value = response.data;
  }).catch((error) => {
    $q.notify({ type: 'negative', position: 'top', message: error.response.data.name || 'Error al listar tipos de dispositivo.' })
  });
}

function save(): void {
  servicesList.postTypeDeviceService(form.value as TypeDevice).then(() => {
    modal.value = false
    listTypeDevice()
    $q.notify({ type: 'positive', position: 'top', message: 'Se creó el registro exitosamente.' })
  }).catch((error) => {
    $q.notify({ type: 'negative', position: 'top', message: error.response.data.name || 'Error al guardar tipos de dispositivo.' })
  });
}

function editRow(): void {
  servicesList.putTypeDeviceService(form.value.id as number, form.value as TypeDevice).then(() => {
    editMode.value = false
    modal.value = false
    listTypeDevice()
    $q.notify({ type: 'positive', position: 'top', message: 'Se actualizó el registro exitosamente.' })
  }).catch((error) => {
    $q.notify({ type: 'negative', position: 'top', message: error.response.data.name || 'Error al editar tipos de dispositivo.' })
  });
}

function deleteRow(data: TypeDevice): void {
  $q.dialog({
    title: 'Confirmar',
    message: '¿Está seguro de eliminar este registro?',
    persistent: true,
    ok: {
      label: 'Eliminar',
      color: 'red'
    },
    cancel: {
      label: 'Cancelar',
      color: 'primary'
    }
  }).onOk(() => {
    servicesList.deleteTypeDeviceService(data).then(() => {
      listTypeDevice()
      $q.notify({ type: 'positive', position: 'top', message: 'Se eliminó el registro exitosamente.' })
    }).catch(() => {
      $q.notify({ type: 'negative', position: 'top', message: 'Error al eliminar tipos de dispositivo.' })
    });
  })
}
</script>