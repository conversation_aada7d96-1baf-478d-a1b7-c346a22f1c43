<template>
    <q-page padding>
        <q-table flat bordered title="Módulo de administración de usuarios" :rows="rows" row-key="name" :filter="filter"
            :columns="columns">
            <template v-slot:top-right>
                <q-btn color="primary" label="Agregar" @click="modalAdd()" icon="add" />
            </template>
            <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                    <q-btn color="info" icon="edit" @click="modalEdit(props.row)" dense flat>
                        <q-tooltip>
                            Editar
                        </q-tooltip>
                    </q-btn>
                    <q-btn color="negative" icon="delete" @click="deleteRow(props.row)" dense flat>
                        <q-tooltip>
                            Eliminar
                        </q-tooltip>
                    </q-btn>
                </q-td>
            </template>
        </q-table>
        <q-dialog v-model="modal" persistent :backdrop-filter="'blur(4px) saturate(150%)'">
            <q-card style="width: 300px">
                <q-card-section>
                    <q-btn flat dense icon="close" class="float-right" v-close-popup />
                    <span class="text-subtitle1">Agregar usuario</span>
                </q-card-section>
                <q-card-section>
                    <q-form class="q-py-md" @submit="editMode ? editRow() : save()" greedy>
                        <q-input v-model="form.username" type="text" label="Usuario" reactive-rules
                            :rules="[val => !!val || 'Este campo es obligatorio']" :disable="editMode"/>
                        <q-checkbox left-label v-model="form.isStaff" label="¿Es Administrador?" />
                        <div class="text-center"></div>
                        <q-checkbox left-label v-model="form.isActive" label="Esta activo?" v-if="editMode" />
                        <div class="text-center">
                            <q-btn color="primary" class="q-mt-md" icon="save"
                                :label="editMode ? 'Actualizar' : 'Guardar'" type="submit" />
                        </div>
                    </q-form>
                </q-card-section>
            </q-card>
        </q-dialog>
    </q-page>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import UserService from 'src/services/user.service'
import { User } from 'src/common/interfaces/user.interface'
import { useQuasar, QTableProps } from 'quasar';



const $q = useQuasar()
const form = ref<User>({ username: '', isActive: false, isStaff: false});
const servicesList = new UserService()
const rows = ref<User[]>([])
const filter = ref('')
const editMode = ref(false)
const modal = ref(false)
const columns: QTableProps['columns'] = [
    {
        name: 'username',
        required: true,
        label: 'Nombre de usuario',
        align: 'left',
        field:  (row: User) => row.username,
        sortable: true,
    },
    {
        name: 'staf',
        label: '¿Es administrador?',
        align: 'left',
        field: (row: User) => row.isStaff ? 'Si' : 'No',
        sortable: true,
    },
    {
        name: 'active',
        label: 'Estado',
        align: 'left',
        field: (row: User) => row.isActive ? 'Activo' : 'Inactivo',
        sortable: true,
    },
    {
        name: 'actions',
        label: '',
        align: 'left',
        field: '',
    },

];

onMounted(() => {
    list();
})

function modalAdd(): void {
    modal.value = true
    form.value = {  username: '', isActive: true, isStaff: false }
    editMode.value = false
}

function modalEdit(data: User): void {
    modal.value = true
    form.value = data
    editMode.value = true
}

function list(): void {
    servicesList.listUserService().then((response) => {
        rows.value = response.data;
    }).catch((error) => {
        $q.notify({ type: 'negative', position: 'top', message: error.response.data.username || 'Error al listar.' })
    });
}

function save(): void {
    servicesList.postUserService(form.value as User).then(() => {
        modal.value = false
        list()
        $q.notify({ type: 'positive', position: 'top', message: 'Se creo el registro exitosamente.' })
    }).catch((error) => {
        $q.notify({ type: 'negative', position: 'top', message: error.response.data.username || 'Error al guardar.' })
    });
}

function editRow(): void {
    servicesList.putUserService(form.value.id as number, form.value as User).then(() => {
        editMode.value = false
        modal.value = false
        list()
        $q.notify({ type: 'positive', position: 'top', message: 'Se actualizo el registro exitosamente.' })
    }).catch((error) => {
        $q.notify({ type: 'negative', position: 'top', message: error.response.data.username || 'Error al actualizar.' })
    });
}

function deleteRow(data: User): void {
    $q.dialog({
        title: 'Confirmar',
        message: 'Esta seguro que desea eliminar este registro?',
        persistent: true,
        ok: {
            label: 'Eliminar',
            color: 'red'
        },
        cancel: {
            label: 'Cancelar',
            color: 'primary'
        }
    }).onOk(() => {
        servicesList.deleteUserService(data).then(() => {
            list()
            $q.notify({ type: 'positive', position: 'top', message: 'Se elimino el registro exitosamente.' })
        }).catch(() => {
            $q.notify({ type: 'negative', position: 'top', message: 'Error al eliminar.' })
        });
    })

}
</script>