<template>
  <BaseAdminComponent
    title="Ciudades"
    subtitle="Agregar ciudad"
    :getService="getBaseCityService"
    :postService="postBaseCityService"
    :putService="putBaseCityService"
    :deleteService="deleteBaseCityService"
  />
</template>

<script setup lang="ts">
import BaseAdminComponent from 'src/components/admin/BaseAdminComponent.vue';
import AdminServices from 'src/services/admin.service';
import { Common } from 'src/common/interfaces/common.interface';

const servicesList = new AdminServices();
const getBaseCityService = () => servicesList.getBaseCityService();
const postBaseCityService = (data: Common) => servicesList.postBaseCityService(data);
const putBaseCityService = (id: number, data: unknown) => servicesList.putBaseCityService(id, data);  
const deleteBaseCityService = (data: Common) => servicesList.deleteBaseCityService(data);
</script>