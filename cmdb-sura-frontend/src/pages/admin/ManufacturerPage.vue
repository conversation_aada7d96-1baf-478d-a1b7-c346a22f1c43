<template>
  <BaseAdminComponent
    title="Fabricantes"
    subtitle="Agregar fabicante"
    :getService="getManufacturerService"
    :postService="postManufacturerService"
    :putService="putManufacturerService"
    :deleteService="deleteManufacturerService"
  />
</template>

<script setup lang="ts">
import BaseAdminComponent from 'src/components/admin/BaseAdminComponent.vue';
import AdminServices from 'src/services/admin.service';
import { Common } from 'src/common/interfaces/common.interface';

const servicesList = new AdminServices();
const getManufacturerService = () => servicesList.getManufacturerService();
const postManufacturerService = (data: Common) => servicesList.postManufacturerService(data);
const putManufacturerService = (id: number, data: unknown) => servicesList.putManufacturerService(id, data);  
const deleteManufacturerService = (data: Common) => servicesList.deleteManufacturerService(data);

</script>