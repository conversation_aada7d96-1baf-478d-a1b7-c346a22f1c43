<template>
  <BaseAdminComponent
    title="Areas"
    subtitle="Agregar area"
    :getService="getAreaService"
    :postService="postAreaService"
    :putService="putAreaService"
    :deleteService="deleteAreaService"
  />
</template>

<script setup lang="ts">
import BaseAdminComponent from 'src/components/admin/BaseAdminComponent.vue';
import AdminServices from 'src/services/admin.service';
import { Common } from 'src/common/interfaces/common.interface';

const servicesList = new AdminServices();
const getAreaService = () => servicesList.getAreaService();
const postAreaService = (data: Common) => servicesList.postAreaService(data);
const putAreaService = (id: number, data: unknown) => servicesList.putAreaService(id, data);  
const deleteAreaService = (data: Common) => servicesList.deleteAreaService(data);
</script>