<template>
  <div>
    <q-card>
      <q-card-section>
        {{ message }}
      </q-card-section>
    </q-card>
  </div>
</template>
<script setup lang="ts">
import type { AxiosError } from 'axios';
import { useQuasar, QSpinnerGears } from 'quasar';
import AuthenticationService from 'src/services/authentication.service';
import { usersDataStore } from 'src/stores/users-store';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const $q = useQuasar();
const $route = useRoute();
const store = usersDataStore();

const authService = new AuthenticationService();
const code: string = $route.query?.code as string ?? '';
const message = ref<string>('validando la información. Por favor espere.');
const redirectUrl = `${process.env.FRONT_URL ?? ''}`;
const sessionState = $route?.query?.session_state ?? '';
const url =
  `${process.env.KEYCLOAK_URL ?? ''}realms/${process.env.KEYCLOAK_REALM ?? ''}` +
  `/protocol/openid-connect/auth/?client_id=${process.env.KEYCLOAK_CLIENT ?? ''}` +
  `&redirect_uri=${redirectUrl}&response_type=code&scope=openid`;

onMounted(() => {
  validateParams();
});

function login(): void {
  $q.loading.show({
    spinner: QSpinnerGears,
    message: 'Un momento por favor...',
    messageColor: 'white',
  });
  authService
    .loginKeycloak(code, redirectUrl)
    .then(({ data }) => {
      store.auth(data);
    })
    .catch((error: AxiosError<Record<string, string>>) => {
      message.value =
        error.response?.data?.detail ??
        'Error al autenticar, por favor intente de nuevo o contacte con el administrador';
      $q.notify({
        type: 'negative',
        position: 'top',
        message: 'Error al momento de autenticar',
      });
    })
    .finally(() => {
      $q.loading.hide();
    });
}

function validateParams(): void {
  if (!sessionState || code.length === 0) {
    window.location.href = url;
  } else {
    login();
  }
}
</script>
