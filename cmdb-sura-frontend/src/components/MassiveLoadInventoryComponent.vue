<template>
  <q-uploader :factory="factoryUpload" field-name="file" accept=".xlsx, .xls" @uploaded="onUploaded" @failed="onFailed" @rejected="onRejected" auto-upload style="width: 100%">
    <template v-slot:header="scope">
      <div class="row no-wrap items-center q-pa-sm q-gutter-xs">
        <q-btn v-if="scope.queuedFiles.length > 0" icon="clear_all" @click="scope.removeQueuedFiles" round dense flat>
          <q-tooltip>Borrar todo</q-tooltip>
        </q-btn>
        <q-btn v-if="scope.uploadedFiles.length > 0" icon="done_all" round dense
          flat>
          <q-tooltip>Eliminar archivos subidos</q-tooltip>
        </q-btn>
        <q-spinner v-if="scope.isUploading" class="q-uploader__spinner" />
        <div class="col">
          <div class="q-uploader__title">Subir archivo</div>
          <div class="q-uploader__subtitle">{{ scope.uploadSizeLabel }} / {{ scope.uploadProgressLabel }}
          </div>
        </div>
        <q-btn v-if="scope.canAddFiles" icon="download" @click="generateTemplate()" round dense flat>
          <q-tooltip>Descargar plantilla</q-tooltip>
        </q-btn>
        <q-btn v-if="scope.canAddFiles" type="a" icon="add_box" @click="scope.pickFiles" round dense flat>
          <q-uploader-add-trigger />
          <q-tooltip>Seleccionar archivos</q-tooltip>
        </q-btn>
        <q-btn v-if="scope.canUpload" icon="cloud_upload" @click="scope.upload" round dense flat>
          <q-tooltip>Subir archivos</q-tooltip>
        </q-btn>
        <q-btn v-if="scope.isUploading" icon="clear" @click="scope.abort" round dense flat>
          <q-tooltip>Cancelar carga</q-tooltip>
        </q-btn>
        <q-dialog v-model="loadReport" persistent>
          <q-card >
            <q-card-section>
              <q-btn class="float-right" icon="close" flat v-close-popup />
              <div class="text-h6">Reporte de carga masiva</div>
            </q-card-section>
            <q-card-section class="q-pa-xs">
              <LoadReportComponent :report-data="reportData"/>
            </q-card-section>
          </q-card>
        </q-dialog>
      </div>
    </template>
  </q-uploader>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useQuasar, QUploaderFactoryObject, QRejectedEntry } from 'quasar';
import CommonServices from 'src/services/common.service';
import { api } from 'src/boot/axios';
import LoadReportComponent from 'src/components/LoadReportComponent.vue';
import { ResponseData } from 'src/common/interfaces/common.interface';
import { downloadExcelFile } from 'src/common/utils/download';

const $q = useQuasar();
const servicesList = new CommonServices();
const loadReport = ref<boolean>(false);
const url = `${api.getUri()}/import/`;
const reportData = ref<XMLHttpRequest>(new XMLHttpRequest);
const emit = defineEmits<{
  (e: 'close-massive-load'): void, (e: 'closeModal'): void
}>();
const props = defineProps<{
  tab: string;
}>();
const tab = ref<string>(props.tab === 'inventory' ? 'create' : 'delete');
defineOptions({
  name: 'MassiveLoadInventoryComponent'
});

function generateTemplate(): void {
  const action = props.tab === 'inventory' ? 'create' : 'delete';
  servicesList.generateTemplateService(action).then((response) => {
    downloadExcelFile(response, $q);
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al descargar la plantilla.' })
  });
}

function factoryUpload(files: readonly File[]): Promise<QUploaderFactoryObject> {
  return new Promise<QUploaderFactoryObject> ((resolve) => {
    files.forEach(file => {
    $q.notify({
      type: 'info',
      spinner: true,
      position: 'top',
      message: `Inicia carga del archivo ${file.name}.`,
      timeout: 1
    })
    });
    resolve({
      url: url,
      method: 'POST',
      formFields: [{name: 'action', value: tab.value}], 
    })
  })
}

function onUploaded(info: { files: readonly File[], xhr: XMLHttpRequest }): void {
  reportData.value = info.xhr
  const responseData: ResponseData = JSON.parse(info.xhr.responseText)
  if(responseData.success){
    $q.notify({
    type: 'positive',
    position: 'top',
    message: 'Archivo cargado de forma exitosa.'
    })
    emit('closeModal');
    setTimeout(() => {
      emit('close-massive-load');
    }, 100);
  }
  else{
    loadReport.value = true
    $q.notify({
    type: 'negative',
    position: 'top',
    message: 'No se cargó el archivo.'
    })
  }
}

function onFailed(info: { files: readonly File[], xhr: XMLHttpRequest }): void {
  const error =JSON.parse(info.xhr.response)
  emit('close-massive-load');
  $q.notify({
    type: 'negative',
    position: 'top',
    message: `Error en la carga. ${error.message || error.error} `,
    actions: [
      { icon: 'close', color: 'white', round: true }
    ]
  })
}

function onRejected(rejectedEntries: QRejectedEntry[]): void  {
  rejectedEntries.forEach(entry => {
    let reason = '';
    if (entry.failedPropValidation === 'accept') {
      reason = 'Tipo de archivo no válido. Sólo se permiten archivos Excel (.xlsx, .xls).';
    }
    else {
      reason = 'El archivo fue rechazado por motivos desconocidos.';
    }
    $q.notify({
      type: 'negative',
      position: 'top',
      message: `Error: ${entry.file.name} - ${reason}`,
      actions: [
        { icon: 'close', color: 'white', round: true }
      ]
    });
  });
}

</script>
