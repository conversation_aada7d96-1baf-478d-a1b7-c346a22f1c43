<template>
  <div class="q-col-gutter-md row items-start">
    <q-card class="my-card .col-12 .col-md-auto">
      <q-card-section>
        <div class="q-my-md col-md-3 col-12 text-body2">{{ responseData.message }}</div>
        <q-space />
        <div class="q-py-sm col-md-3 col-12 text-body2">Total de dispositivos procesados: {{ responseData.totalProcessed }}</div>
        <div class="q-py-sm col-md-3 col-12 text-body2">Dispositivos válidos: {{ responseData.validRegisters }}</div>
        <div class="q-py-sm col-md-3 col-12 text-body2">Dispositivos no válidos: {{ invalidDevices }}</div>
        <div class="q-py-sm col-md-3 col-12 text-body2">Celdas con error: {{ responseData.registersWithErrors }}</div>
      </q-card-section>
      <q-card-section>
        <div class="col-md-3 col-12">
          <q-table
          title="Detalle de errores"
          :rows="rows"
          :columns="columns"
          row-key="cell"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import  { ResponseError, ResponseData, DefaultResponseData} from 'src/common/interfaces/common.interface';
import {  QTableProps } from 'quasar';

defineOptions({
  name: 'LoadReportComponent'
});

const props = defineProps<{
  reportData: XMLHttpRequest;
}>();

const responseData = ref<ResponseData>(DefaultResponseData)
const rows = ref<ResponseError[]>(DefaultResponseData.errors);
const invalidDevices = computed((): number => 
  responseData.value.totalProcessed - responseData.value.validRegisters
)
const columns: QTableProps['columns'] = [
  {
    name: 'cell',
    align: 'center',
    label: 'Celda',
    field: (row: ResponseError) => row.cell,
    format: (val: number) => `${val}`,
    sortable: true
  },
  {
    name: 'errors',
    align: 'center',
    label: 'Errores',
    field: (row: ResponseError) => row.error,
    format: (val: string) => `${val}`,
    sortable: true
  },
]

onMounted(() => {  
  setRows()
})

function setRows(): void {
  responseData.value = JSON.parse(props.reportData.responseText)
  rows.value = responseData.value.errors.map(error=> ({
    cell: error.cell,
    error: error.error
  }));
}

</script>