<template>
  <q-card-section>
    <q-form @submit.prevent="submitForm" class="row q-col-gutter-md" greedy>
      <div class="col-md-3 col-12">
        <q-select option-label="name" option-value="id" emit-value map-options :options="listBaseCity"
          v-model="formData.baseCity" type="text" label="Ciudad base*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-select option-label="name" option-value="id" emit-value map-options :options="listBaseHeadquarters"
          v-model="formData.baseHeadquarters" type="text" label="Sede base*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.location" type="text" label="Ubicación" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.document" type="text" label="Cédula*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-select option-label="name" option-value="id" emit-value map-options :options="listCompany"
          v-model="formData.company" type="text" label="Compañía*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.username" type="text" label="Nombre de usuario*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-select option-label="name" option-value="id" map-options :options="listTypeDevice" v-model="formData.typeDevice" label="Tipo dispositivo*"
          @update:model-value="changeTypeDevice()" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-select option-label="name" option-value="id" emit-value map-options :options="listArea"
          v-model="formData.area" type="text" label="Área*" :disable="!isAreaFloorFieldEnabled || !staff" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" />
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.floor" type="text" label="Piso*" :disable="!isAreaFloorFieldEnabled || !staff" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" />
      </div>
      <div class="col-md-3 col-12">
        <q-select option-label="name" option-value="id" emit-value map-options :options="listManufacturer"
          v-model="formData.manufacturer" type="text" label="Fabricante*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.model" type="text" label="Modelo*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.serial" type="text" label="Serial*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.licensePlate" type="text" label="Placa" reactive-rules
         :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.ownership" type="text" label="Propiedad*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.warehouseEntryDate" label="Fecha de ingreso a bodega*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.', val => extractYear(val) > 2015 || 'La fecha de ingreso a bodega debe ser posterior al año 2015.']" :disable="!staff">
          <template v-slot:prepend>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-date v-model="formData.warehouseEntryDate" mask="YYYY-MM-DD"
                  @update:model-value="calculateDateRenewal()">
                  <div class="row items-center justify-end">
                    <q-btn v-close-popup label="Close" color="primary" flat />
                  </div>
                </q-date>
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.networkUser" type="text" label="Usuario de red" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.modality" type="text" label="Modalidad*" disable reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" />
      </div>
      <div class="col-md-3 col-12">
        <q-select option-label="name" option-value="id" use-chips emit-value map-options :options="listModalityTag"
          v-model="formData.modalityTag" type="text" label="Etiqueta de modalidad" :disable="!isModalityTagEnabled || !staff" />
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.comments" type="text" label="Observaciones" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-select option-label="name" option-value="id" emit-value map-options :options="listStatusDevice"
          v-model="formData.status" type="text" label="Estado*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.']" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.machineName" type="text" label="Nombre máquina" reactive-rules :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.warrantyDate" type="text" label="Fecha de garantía*" reactive-rules
          :rules="[val => !!val || 'Este campo es obligatorio.', val => extractYear(val) > 2015 || 'La fecha de garantía debe ser posterior al año 2015.']" :disable="!staff">
          <template v-slot:prepend>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-date v-model="formData.warrantyDate" mask="YYYY-MM-DD">
                  <div class="row items-center justify-end">
                    <q-btn v-close-popup label="Close" color="primary" flat />
                  </div>
                </q-date>
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.os" type="text" label="SO" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.processorAndSpeed" type="text" label="Procesador y velocidad" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.hardDrive" type="text" label="Disco duro" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.memory" type="text" label="Memoria" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.totalMemoryBanks" type="text" label="Total bancos de memoria" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.usedBanks" type="text" label="Bancos usados" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.freeBanks" type="text" label="Bancos libres" :disable="!staff"/>
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.technologyRenewalDate" disable type="text" label="Fecha de renovación tecnológica*"
          reactive-rules :rules="[val => !!val || 'Este campo es obligatorio.']" />
      </div>
      <div class="col-md-3 col-12">
        <q-input v-model="formData.deleteCause" disable type="text" label="Causal de baja" v-show="props.tab === 'deleted'"/>
      </div>
      <div class="col-12 q-pt-md flex justify-between" v-show="staff && props.tab === 'inventory'">
        <q-btn :label="editData ? 'Actualizar' : 'Guardar' " type="submit" color="primary" :loading="loading" />          
        <q-btn v-show="editData" icon="delete" @click="confirmDelete = true" color="negative" />
      </div>
    </q-form>
  </q-card-section>
  <q-dialog v-model="confirmDelete" persistent>
    <q-card>
      <q-card-section>
        <q-btn class="float-right" icon="close" flat v-close-popup />
        <div class="text-h6">¿Está seguro de eliminar este elemento?</div>
      </q-card-section>
      <q-card-section>
        <q-form @submit.prevent="deleteInventory" class="column q-col-gutter-md" greedy>
          <div class="col-md-3 col-12">
            <q-input v-model="formData.deleteCause" type="text" label="Causal de baja*" reactive-rules
              :rules="[val => !!val || 'Este campo es obligatorio.']" />
          </div>
          <div class="col-md-3 col-12 q-pt-md flex justify-center">
            <q-btn label="Eliminar" color="negative" type="submit" />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useQuasar } from 'quasar';
import CommonServices from 'src/services/common.service'
import AdminServices from 'src/services/admin.service'
import { Common, FormInventory, TypeDevice, DefaultValuesInventory } from 'src/common/interfaces/common.interface'
import { usersDataStore } from 'src/stores/users-store'

defineOptions({
  name: 'RegisterInventoryComponent'
});

const $q = useQuasar();
const loading = ref<boolean>(false);
const editData = ref<boolean>(false);
const servicesList = new CommonServices();
const adminServicesList = new AdminServices();
const formData = ref<FormInventory>({ ...DefaultValuesInventory });
const listBaseCity = ref<Common[]>([]);
const listBaseHeadquarters = ref<Common[]>([]);
const listCompany = ref<Common[]>([]);
const listTypeDevice = ref<TypeDevice[]>([]);
const listArea = ref<Common[]>([]);
const listModalityTag = ref<Common[]>([]);
const listManufacturer = ref<Common[]>([]);
const listStatusDevice = ref<Common[]>([]); 
const usersData = usersDataStore()
const staff = ref(usersData.getStaff);
const confirmDelete = ref<boolean>(false);
const props = defineProps<{
  dataInventory?: FormInventory,
  tab: string
}>()
const emit = defineEmits<(e: 'closeModal') => void>();

onMounted(() => {
  getTypeDeviceService()
  getBaseCityService()
  getBaseHeadquartersService()
  getCompanyService()
  getAreaService()
  getModalityTagService()
  getManufacturerService()
  getStatusDeviceService()
  if (props.dataInventory) {
    formData.value = props.dataInventory;
    editData.value = true;
  }
})

function getTypeDeviceService(): void {  
  servicesList.getTypeDeviceService().then((response) => {
    listTypeDevice.value = response.data
    if (editData.value) {
      formData.value.typeDevice = listTypeDevice.value?.find((x) => x.id == props.dataInventory?.typeDevice) as TypeDevice
    }
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al listar tipos de dispositivos.' })
  });
}

function getBaseCityService(): void {
  servicesList.getBaseCityService().then((response) => {
    listBaseCity.value = response.data
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al listar ciudades base.' })
  });
}

function getBaseHeadquartersService(): void {
  servicesList.getBaseHeadquartersService().then((response) => {
    listBaseHeadquarters.value = response.data
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al listar sedes base.' })
  });
}

function getCompanyService(): void {
  servicesList.getCompanyService().then((response) => {
    listCompany.value = response.data
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al listar compañías.' })
  });
}

function getAreaService(): void {
  servicesList.getAreaService().then((response) => {
    listArea.value = response.data
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al listar áreas.' })
  });
}

function getModalityTagService(): void {
  servicesList.getModalityTagService().then((response) => {
    listModalityTag.value = response.data
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al listar etiquetas de modalidad.' })
  });
}

function getManufacturerService(): void {
  servicesList.getManufacturerService().then((response) => {
    listManufacturer.value = response.data
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al listar fabricantes.' })
  });
}

function getStatusDeviceService(): void {
  servicesList.getStatusDeviceService().then((response) => {
    listStatusDevice.value = response.data
  }).catch(() => {
    $q.notify({ type: 'negative', position: 'top', message: 'Error al listar estados de dispositivo.' })
  });
}

function changeTypeDevice(): void {
  formData.value.modality = formData.value.typeDevice?.deviceClass ?? ''
  if (!isAreaFloorFieldEnabled.value) {
      formData.value.area = null;
    }
  if (formData.value.warehouseEntryDate !== '') {
    calculateDateRenewal()
  }
}

const isAreaFloorFieldEnabled = computed(() => {
  return formData.value.modality.toLowerCase() === 'fijo';
});

const isModalityTagEnabled = computed(() => {
  return formData.value.modality.toLowerCase() === 'movil';
});

function calculateDateRenewal(): void {
  let renewalDate = new Date(formData.value.warehouseEntryDate);
  renewalDate.setFullYear(renewalDate.getFullYear() + Number(formData.value.typeDevice?.years ?? 0));
  formData.value.technologyRenewalDate = renewalDate.toISOString().split('T')[0];
}

function submitForm(): void {
  const typeDeviceValue = formData.value.typeDevice?.id
  let dataSend = { ...formData.value, typeDevice: typeDeviceValue }
  let service = editData.value ? servicesList.putInventoryService(props.dataInventory?.id as number, dataSend) : servicesList.postInventoryService(dataSend)
  service.then(() => {
    formData.value = { ...DefaultValuesInventory };
    $q.notify({ type: 'positive', position: 'top', message: editData.value ? 'Se Actualizó exitosamente.' : 'Se registró exitosamente.' });
    emit('closeModal');
  }).catch((error) => {
    $q.notify({ type: 'negative', position: 'top', message: error.response.data.message || error.response.data.machine_name || error.response.data.serial || error.response.data.license_plate || 'Error al guardar el activo.' })
  })
};

function deleteInventory(): void {
  if (formData.value.typeDevice?.id) {
    const typeDeviceValue = formData.value.typeDevice?.id
    adminServicesList.deleteInventoryService(formData.value, typeDeviceValue).then(() => {
      $q.notify({ type: 'positive', position: 'top', message: 'Se eliminó exitosamente.' });
      emit('closeModal');
    }).catch((error) => {
    $q.notify({ type: 'negative', position: 'top', message: error.response.data.message || 'Error al eliminar el activo.' })
    })
  }
}

function extractYear(dateString: string): number {
  return parseInt(dateString.substring(0, 4));
}
</script>