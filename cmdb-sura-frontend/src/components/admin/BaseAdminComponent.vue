<template>
  <q-page padding>
    <q-table
      flat
      bordered
      :title="props.title"
      :rows="rows"
      :columns="columns"
      row-key="name"
      :filter="filter"
    >
      <template v-slot:top-right>
        <q-btn color="primary" label="Agregar" @click="modalAdd()" icon="add" />
      </template>
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <q-btn color="info" icon="edit" @click="modalEdit(props.row)" dense flat>
            <q-tooltip>Editar</q-tooltip>
          </q-btn>
          <q-btn color="negative" icon="delete" @click="deleteRow(props.row)" dense flat>
            <q-tooltip>Eliminar</q-tooltip>
          </q-btn>
        </q-td>
      </template>
    </q-table>
    <q-dialog v-model="modal" persistent>
      <q-card style="width: 300px">
        <q-card-section>
          <q-btn flat dense icon="close" class="float-right" v-close-popup />
          <span class="text-subtitle1">{{ props.subtitle }}</span>
        </q-card-section>
        <q-card-section>
          <q-form class="q-py-md" @submit="editMode ? editRow() : save()" greedy>
            <q-input v-model="form.name" type="text" label="Nombre" :rules="[val => !!val || 'Este campo es obligatorio']" />
            <q-checkbox left-label v-model="form.status" label="Esta activo?" v-if="editMode" />
            <div class="text-center">
              <q-btn color="primary" class="q-mt-md" icon="save" :label="editMode ? 'Actualizar' : 'Guardar'" type="submit" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useQuasar, QTableProps } from 'quasar';
import { BaseService, Common } from 'src/common/interfaces/common.interface';

const props = defineProps<{
  title: string,
  subtitle: string,
  getService: BaseService['getService'],
  postService: BaseService['postService'],
  putService: BaseService['putService'],
  deleteService: BaseService['deleteService'],
}>();

defineOptions({
  name: 'BaseAdminComponent',
});

const $q = useQuasar()
const form = ref<Common>({ name: '', status: true });
const rows = ref<Common[]>([])
const filter = ref('')
const editMode = ref(false)
const modal = ref(false)

const columns: QTableProps['columns'] = [
  { name: 'actions', label: 'Acciones', align: 'left', field: '' },
  { name: 'name', required: true, label: 'Nombre', align: 'left', field: row => row.name, sortable: true },
  { name: 'status', label: 'Estado', align: 'left', field: row => row.status ? 'Activo' : 'Inactivo', sortable: true },
];

onMounted(() => {
  list();
});

function modalAdd(): void {
    modal.value = true
    form.value = { name: '', status: true }
    editMode.value = false
}

function modalEdit(data: Common): void {
    modal.value = true
    form.value = data
    editMode.value = true
}

function list(): void {
  props.getService().then((response) => {
    rows.value = response.data;
  }).catch((error) => {
    $q.notify({ type: 'negative', position: 'top', message: error.response?.data?.name || 'Error al listar.' });
  });
}


function save(): void {
  props.postService(form.value as Common).then(() => {
    modal.value = false;
    list();
    $q.notify({ type: 'positive', position: 'top', message: 'Se creó el registro exitosamente.' });
  }).catch((error) => {
    $q.notify({ type: 'negative', position: 'top', message: error.response?.data?.name || 'Error al guardar.' });
  });
}


function editRow(): void {
  props.putService(form.value.id as number, form.value as Common).then(() => {
    editMode.value = false;
    modal.value = false;
    list();
    $q.notify({ type: 'positive', position: 'top', message: 'Se actualizó el registro exitosamente.' });
  }).catch((error) => {
    $q.notify({ type: 'negative', position: 'top', message: error.response?.data?.name || 'Error al editar.' });
  });
}

function deleteRow(data: Common): void {
  $q.dialog({
    title: 'Confirmar',
    message: '¿Está seguro de eliminar este registro?',
    persistent: true,
    ok: { label: 'Eliminar', color: 'red' },
    cancel: { label: 'Cancelar', color: 'primary' }
  }).onOk(() => {
    props.deleteService(data).then(() => {
      list();
      $q.notify({ type: 'positive', position: 'top', message: 'Se eliminó el registro exitosamente.' });
    }).catch(() => {
      $q.notify({ type: 'negative', position: 'top', message: 'Error al eliminar.' });
    });
  });
}


</script>