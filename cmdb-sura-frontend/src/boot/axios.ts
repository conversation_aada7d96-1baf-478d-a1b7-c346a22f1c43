import { boot } from 'quasar/wrappers';
import axios, { AxiosInstance, AxiosError, AxiosResponse } from 'axios';
import { usersDataStore } from 'src/stores/users-store';
import { useRouter } from 'vue-router';

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $axios: AxiosInstance;
    $api: AxiosInstance;
    $powerbiApi: AxiosInstance;
  }
}

const api: AxiosInstance = axios.create({ baseURL: process.env.BACKEND_URL });
api.interceptors.request.use(
  (config) => {
    const token = usersDataStore().accessToken ?? null;
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error: AxiosError): Promise<AxiosError> => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    return response;
  },
  async (error: AxiosError): Promise<AxiosError> => {
    if (error.response?.status === 401) {
      const router = useRouter();
      usersDataStore().clean();
      await router.push('/');
    }
    return Promise.reject(error);
  }
);

const powerbiApi: AxiosInstance = axios.create({ baseURL: process.env.TOKEN_URL });

export default boot(({ app }) => {
  app.config.globalProperties.$axios = axios;
  app.config.globalProperties.$api = api;
  app.config.globalProperties.$powerbiApi = powerbiApi;
});

export { api, powerbiApi };