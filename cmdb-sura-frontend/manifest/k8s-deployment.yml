---
apiVersion: v1
kind: Namespace
metadata:
  name: parameters.namespace

---
apiVersion: v1
data:
  .dockerconfigjson: ************************************************************************************************************************************************************
kind: Secret
metadata:
  name: regcred
  namespace: parameters.namespace
type: kubernetes.io/dockerconfigjson

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: parameters.imageRepository
  namespace: parameters.namespace
spec:
  selector:
    matchLabels:
      app: parameters.imageRepository
  template:
    metadata:
      labels:
        app: parameters.imageRepository
    spec:
      containers:
        - name: parameters.imageRepository
          image: parameters.containerRegistry/parameters.harborProject/parameters.imageRepository:parameters.tag
          imagePullPolicy: Always
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
              ephemeral-storage: "100Mi"
            limits:
              memory: "256Mi"
              cpu: "100m"
              ephemeral-storage: "2Gi"
          ports:
            - containerPort: 8080
      imagePullSecrets:
        - name: regcred
      automountServiceAccountToken: false
---

apiVersion: v1
kind: Service
metadata:
  name: parameters.imageRepository
  namespace: parameters.namespace
spec:
  selector:
    app: parameters.imageRepository
  type: ClusterIP
  ports:
    - name: parameters.imageRepository
      protocol: TCP
      port: 8080
      targetPort: 8080
