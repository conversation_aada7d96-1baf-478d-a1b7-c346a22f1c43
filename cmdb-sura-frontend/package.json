{"name": "file-viewer", "version": "0.0.1", "description": "CMDB SURA", "productName": "CMDB SURA", "author": "ARUS", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@quasar/extras": "^1.16.4", "axios": "^1.2.1", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "pinia": "^2.0.11", "pinia-plugin-store": "^3.0.0", "powerbi-client": "^2.23.1", "powerbi-client-vue-js": "^1.1.2", "quasar": "^2.16.0", "vue": "^3.4.18", "vue-router": "^4.0.12"}, "devDependencies": {"@quasar/app-vite": "^1.9.0", "@quasar/quasar-app-extension-qpdfviewer": "^2.0.0-alpha.6", "@types/crypto-js": "^4.2.2", "@types/node": "^12.20.21", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "autoprefixer": "^10.4.2", "eslint": "^8.57.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "typescript": "^4.5.4", "vite-plugin-checker": "^0.6.4", "vue-tsc": "^1.8.22"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}